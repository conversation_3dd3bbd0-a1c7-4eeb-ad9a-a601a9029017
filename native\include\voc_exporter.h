#ifndef VOC_EXPORTER_H
#define VOC_EXPORTER_H

#include "common.h"
#include <string>
#include <vector>

/**
 * VOC格式导出器
 */
class VOCExporter {
public:
    /**
     * 导出VOC格式数据集
     * 
     * @param config 导出配置
     * @param images 图像信息列表
     * @param categories 分类信息JSON
     * @return 导出结果
     */
    ExportResult exportDataset(const ExportConfig& config, 
                              const std::vector<ImageInfo>& images, 
                              const std::string& categories);

private:
    /**
     * 创建VOC格式的XML标注文件
     */
    bool createAnnotationXML(const ImageInfo& image,
                            const std::string& outputPath,
                            const std::string& categories);
    
    /**
     * 处理单个图像
     */
    bool processImage(const ImageInfo& image, 
                     const ExportConfig& config,
                     const std::string& outputDir);
    
    /**
     * 创建数据集分割文件 (train.txt, val.txt, test.txt)
     */
    bool createDatasetSplitFiles(const std::vector<ImageInfo>& images,
                                const std::string& outputPath);
    
    /**
     * 解析标注数据为VOC格式
     */
    std::string parseAnnotationToVOC(const std::string& annotationData,
                                    const ImageInfo& image);
};

#endif // VOC_EXPORTER_H

package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationProjectConfigsService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 标注项目配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注项目配置管理")
@RestController
//@RequestMapping("/annotation/projectConfigs")
@RequestMapping("/annotation/configs")
public class AnnotationProjectConfigsController extends BaseController
{
    @Resource
    private AnnotationProjectConfigsService annotationProjectConfigsService;

    /**
     * 查询标注项目配置列表
     */
    @ApiOperation("查询标注项目配置列表")
    @PreAuthorize("@ss.hasPermi('system:configs:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnotationProjectConfigs annotationProjectConfigs)
    {
        startPage();
        List<AnnotationProjectConfigs> list = annotationProjectConfigsService.selectAnnotationProjectConfigsList(annotationProjectConfigs);
        return getDataTable(list);
    }

    /**
     * 导出标注项目配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:configs:export')")
    @Log(title = "标注项目配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationProjectConfigs annotationProjectConfigs)
    {
        List<AnnotationProjectConfigs> list = annotationProjectConfigsService.selectAnnotationProjectConfigsList(annotationProjectConfigs);
        ExcelUtil<AnnotationProjectConfigs> util = new ExcelUtil<AnnotationProjectConfigs>(AnnotationProjectConfigs.class);
        util.exportExcel(response, list, "标注项目配置数据");
    }

    /**
     * 获取标注项目配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:configs:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return success(annotationProjectConfigsService.selectAnnotationProjectConfigsByConfigId(configId));
    }

    /**
     * 新增标注项目配置
     */
    @PreAuthorize("@ss.hasPermi('system:configs:add')")
    @Log(title = "标注项目配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationProjectConfigs annotationProjectConfigs)
    {
        return toAjax(annotationProjectConfigsService.insertAnnotationProjectConfigs(annotationProjectConfigs));
    }

    /**
     * 修改标注项目配置
     */
    @PreAuthorize("@ss.hasPermi('system:configs:edit')")
    @Log(title = "标注项目配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationProjectConfigs annotationProjectConfigs)
    {
        return toAjax(annotationProjectConfigsService.updateAnnotationProjectConfigs(annotationProjectConfigs));
    }

    /**
     * 删除标注项目配置
     */
    @PreAuthorize("@ss.hasPermi('system:configs:remove')")
    @Log(title = "标注项目配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable("configIds") Long[] configIds)
    {
        return toAjax(annotationProjectConfigsService.deleteAnnotationProjectConfigsByConfigIds(configIds));
    }
}

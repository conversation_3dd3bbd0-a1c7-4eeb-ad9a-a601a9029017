<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationImageSourcesMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationImageSources" id="AnnotationImageSourcesResult">
        <result property="sourceId"    column="source_id"    />
        <result property="sourceName"    column="source_name"    />
        <result property="uploadType"    column="upload_type"    />
        <result property="uploadedByUserId"    column="uploaded_by_user_id"    />
        <result property="uploadedAt"    column="uploaded_at"    />
        <result property="metadata"    column="metadata"    />
        <result property="path"    column="path"    />
        <result property="archivePath"    column="archive_path"    />
        <result property="contentHash"    column="content_hash"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationImageSourcesVo">
        select source_id, source_name, upload_type, uploaded_by_user_id, uploaded_at, metadata, path, archive_path, content_hash, create_time, update_time, create_by, update_by from annotation_image_sources
    </sql>

    <select id="selectAnnotationImageSourcesList" parameterType="com.ylzx.annotation.domain.AnnotationImageSources" resultMap="AnnotationImageSourcesResult">
        <include refid="selectAnnotationImageSourcesVo"/>
        <where>
            <if test="sourceName != null  and sourceName != ''"> and source_name like '%' || #{sourceName} || '%'</if>
            <if test="uploadType != null  and uploadType != ''"> and upload_type = #{uploadType}</if>
            <if test="uploadedByUserId != null "> and uploaded_by_user_id = #{uploadedByUserId}</if>
            <if test="uploadedAt != null  and uploadedAt != ''"> and uploaded_at = #{uploadedAt}</if>
            <if test="metadata != null  and metadata != ''"> and metadata = #{metadata}</if>
        </where>
    </select>

    <!-- MyBatis-Plus 需要的基础查询方法 -->
    <select id="selectList" resultMap="AnnotationImageSourcesResult">
        <include refid="selectAnnotationImageSourcesVo"/>
        <if test="ew != null">
            <if test="ew.entity != null">
                <where>
                    <if test="ew.entity.sourceId != null"> and source_id = #{ew.entity.sourceId}</if>
                    <if test="ew.entity.sourceName != null and ew.entity.sourceName != ''"> and source_name = #{ew.entity.sourceName}</if>
                    <if test="ew.entity.uploadType != null and ew.entity.uploadType != ''"> and upload_type = #{ew.entity.uploadType}</if>
                    <if test="ew.entity.uploadedByUserId != null"> and uploaded_by_user_id = #{ew.entity.uploadedByUserId}</if>
                    <if test="ew.entity.uploadedAt != null and ew.entity.uploadedAt != ''"> and uploaded_at = #{ew.entity.uploadedAt}</if>
                    <if test="ew.entity.metadata != null and ew.entity.metadata != ''"> and metadata = #{ew.entity.metadata}</if>
                    <if test="ew.entity.path != null and ew.entity.path != ''"> and path = #{ew.entity.path}</if>
                    <if test="ew.entity.archivePath != null and ew.entity.archivePath != ''"> and archive_path = #{ew.entity.archivePath}</if>
                    <if test="ew.entity.contentHash != null and ew.entity.contentHash != ''"> and content_hash = #{ew.entity.contentHash}</if>
                    <if test="ew.entity.createBy != null and ew.entity.createBy != ''"> and create_by = #{ew.entity.createBy}</if>
                    <if test="ew.entity.updateBy != null and ew.entity.updateBy != ''"> and update_by = #{ew.entity.updateBy}</if>
                </where>
            </if>
            <if test="ew.sqlSegment != null and ew.sqlSegment != ''">
                ${ew.sqlSegment}
            </if>
        </if>
    </select>

    <!-- 根据内容哈希检查是否存在重复记录 -->
    <select id="countByContentHash" resultType="int">
        SELECT COUNT(1) FROM annotation_image_sources WHERE content_hash = #{contentHash}
    </select>

</mapper>
@echo off
REM Windows构建脚本

echo 开始构建数据集导出本地库...

REM 创建构建目录
if not exist build mkdir build
cd build

REM 配置CMake
echo 配置CMake...
cmake .. -G "MinGW Makefiles"

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败
    pause
    exit /b 1
)

REM 构建项目
echo 构建项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 构建失败
    pause
    exit /b 1
)

echo 构建完成！
echo 库文件位置: build\Release\dataset_export.dll

REM 复制到Java资源目录
if exist "..\ylzx-annotation\src\main\resources\native" (
    echo 复制库文件到Java资源目录...
    copy Release\dataset_export.dll "..\ylzx-annotation\src\main\resources\native\"
)

pause

//package com.ylzx.web.core.exception;
//
//import com.alibaba.cola.dto.Response;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.support.DefaultMessageSourceResolvable;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.converter.HttpMessageConversionException;
//import org.springframework.validation.BindException;
//import org.springframework.validation.ObjectError;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.ResponseStatus;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//import jakarta.validation.ValidationException;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * 全局异常处理器
// * 用于处理参数校验和其他异常
// *
// * <AUTHOR>
// * @date 2025-06-24
// */
//@Slf4j
//@RestControllerAdvice
//public class GlobalExceptionHandler {
//
//    /**
//     * 处理参数校验异常
//     */
//    @ResponseBody
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(BindException.class)
//    public Response handleBindException(BindException e) {
//        // 拼接错误信息，用于多个校验不通过的错误信息拼接
//        List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
//        String message = allErrors.stream()
//                .map(DefaultMessageSourceResolvable::getDefaultMessage)
//                .collect(Collectors.joining(";"));
//        log.info("参数校验不通过：{}", message);
//        return Response.buildFailure("参数校验不通过", message);
//    }
//
//    /**
//     * 处理参数转换异常
//     */
//    @ResponseBody
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(HttpMessageConversionException.class)
//    public Response handleHttpMessageConversionException(HttpMessageConversionException e) {
//        log.info("参数转换失败：{}", e.getMessage());
//        return Response.buildFailure("参数转换失败", e.getMessage());
//    }
//
//    /**
//     * 处理参数验证异常
//     */
//    @ResponseBody
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    @ExceptionHandler(ValidationException.class)
//    public Response handleValidationException(ValidationException e) {
//        log.info("参数验证失败: {}", e.getMessage());
//        return Response.buildFailure("参数验证失败", e.getMessage());
//    }
//
//    /**
//     * 处理其他未知异常
//     */
//    @ResponseBody
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    @ExceptionHandler(Exception.class)
//    public Response handleException(Exception e) {
//        log.warn("未知异常", e);
//        return Response.buildFailure("未知异常", e.getMessage());
//    }
//}

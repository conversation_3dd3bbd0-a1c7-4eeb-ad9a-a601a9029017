package com.ylzx.common.core.utils.annotation;

import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 表示由一系列点定义的多边形.
 *
 * <AUTHOR>
 */
@Getter
public class Polygon implements Shape {

    private final List<Point> points;

    public Polygon(List<Point> points) {
        if (points == null || points.size() < 3) {
            throw new IllegalArgumentException("多边形至少需要3个点。");
        }
        this.points = points;
    }

    @Override
    public String toWkt() {
        return points.stream()
                .map(Point::toString)
                .collect(Collectors.joining(", "));
    }

    @Override
    public String toString() {
        return toWkt();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Polygon polygon = (Polygon) o;
        return Objects.equals(points, polygon.points);
    }

    @Override
    public int hashCode() {
        return Objects.hash(points);
    }
} 
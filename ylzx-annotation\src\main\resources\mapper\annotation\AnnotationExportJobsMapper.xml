<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationExportJobsMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationExportJobs" id="AnnotationExportJobsResult">
        <result property="jobId"    column="job_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="exportFormat"    column="export_format"    />
        <result property="status"    column="status"    />
        <result property="outputPath"    column="output_path"    />
        <result property="jobLog"    column="job_log"    />
        <result property="completedAt"    column="completed_at"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationExportJobsVo">
        select job_id, project_id, export_format, status, output_path, job_log, completed_at, create_time, update_time, create_by, update_by from annotation_export_jobs
    </sql>

    <select id="selectAnnotationExportJobsList" parameterType="AnnotationExportJobs" resultMap="AnnotationExportJobsResult">
        <include refid="selectAnnotationExportJobsVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="exportFormat != null  and exportFormat != ''"> and export_format = #{exportFormat}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="outputPath != null  and outputPath != ''"> and output_path = #{outputPath}</if>
            <if test="jobLog != null  and jobLog != ''"> and job_log = #{jobLog}</if>
            <if test="completedAt != null  and completedAt != ''"> and completed_at = #{completedAt}</if>
        </where>
    </select>
    
    <select id="selectAnnotationExportJobsByJobId" parameterType="Long" resultMap="AnnotationExportJobsResult">
        <include refid="selectAnnotationExportJobsVo"/>
        where job_id = #{jobId}
    </select>

    <insert id="insertAnnotationExportJobs" parameterType="AnnotationExportJobs" useGeneratedKeys="true" keyProperty="jobId">
        insert into annotation_export_jobs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="exportFormat != null and exportFormat != ''">export_format,</if>
            <if test="status != null">status,</if>
            <if test="outputPath != null">output_path,</if>
            <if test="jobLog != null">job_log,</if>
            <if test="completedAt != null">completed_at,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="exportFormat != null and exportFormat != ''">#{exportFormat},</if>
            <if test="status != null">#{status},</if>
            <if test="outputPath != null">#{outputPath},</if>
            <if test="jobLog != null">#{jobLog},</if>
            <if test="completedAt != null">#{completedAt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationExportJobs" parameterType="AnnotationExportJobs">
        update annotation_export_jobs
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="exportFormat != null and exportFormat != ''">export_format = #{exportFormat},</if>
            <if test="status != null">status = #{status},</if>
            <if test="outputPath != null">output_path = #{outputPath},</if>
            <if test="jobLog != null">job_log = #{jobLog},</if>
            <if test="completedAt != null">completed_at = #{completedAt},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where job_id = #{jobId}
    </update>

    <delete id="deleteAnnotationExportJobsByJobId" parameterType="Long">
        delete from annotation_export_jobs where job_id = #{jobId}
    </delete>

    <delete id="deleteAnnotationExportJobsByJobIds" parameterType="String">
        delete from annotation_export_jobs where job_id in 
        <foreach item="jobId" collection="array" open="(" separator="," close=")">
            #{jobId}
        </foreach>
    </delete>
</mapper>
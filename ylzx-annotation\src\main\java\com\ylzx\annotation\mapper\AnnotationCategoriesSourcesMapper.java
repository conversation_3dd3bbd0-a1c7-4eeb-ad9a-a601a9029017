package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationCategoriesSources;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标注分类数据源关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Mapper
public interface AnnotationCategoriesSourcesMapper extends BaseMapper<AnnotationCategoriesSources>
{
    /**
     * 查询标注分类数据源关联列表
     *
     * @param annotationCategoriesSources 标注分类数据源关联
     * @return 标注分类数据源关联集合
     */
    List<AnnotationCategoriesSources> selectAnnotationCategoriesSourcesList(AnnotationCategoriesSources annotationCategoriesSources);

    /**
     * 检查分类和数据源的关联是否存在
     *
     * @param categoryId 分类ID
     * @param sourceId 数据源ID
     * @return 存在的记录数量，0表示不存在，大于0表示存在
     */
    int countByCategoryIdAndSourceId(@Param("categoryId") Long categoryId, @Param("sourceId") Long sourceId);

    /**
     * 根据分类ID查询关联的数据源列表
     *
     * @param categoryId 分类ID
     * @return 关联的数据源ID列表
     */
    List<Long> selectSourceIdsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据数据源ID查询关联的分类列表
     *
     * @param sourceId 数据源ID
     * @return 关联的分类ID列表
     */
    List<Long> selectCategoryIdsBySourceId(@Param("sourceId") Long sourceId);

    /**
     * 批量插入分类数据源关联
     *
     * @param categoriesSources 分类数据源关联列表
     * @return 插入的记录数
     */
    int insertBatch(List<AnnotationCategoriesSources> categoriesSources);

    /**
     * 删除指定分类的所有数据源关联
     *
     * @param categoryId 分类ID
     * @return 删除的记录数
     */
    int deleteByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 删除指定数据源的所有分类关联
     *
     * @param sourceId 数据源ID
     * @return 删除的记录数
     */
    int deleteBySourceId(@Param("sourceId") Long sourceId);
}

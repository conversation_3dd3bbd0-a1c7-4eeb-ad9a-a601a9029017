package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationCategories;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标注分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationCategoriesMapper extends BaseMapper<AnnotationCategories>
{
    /**
     * 查询标注分类
     * 
     * @param categoryId 标注分类主键
     * @return 标注分类
     */
    AnnotationCategories selectAnnotationCategoriesByCategoryId(Long categoryId);

    /**
     * 查询标注分类列表
     * 
     * @param annotationCategories 标注分类
     * @return 标注分类集合
     */
    List<AnnotationCategories> selectAnnotationCategoriesList(AnnotationCategories annotationCategories);

    /**
     * 新增标注分类
     * 
     * @param annotationCategories 标注分类
     * @return 结果
     */
    int insertAnnotationCategories(AnnotationCategories annotationCategories);

    /**
     * 修改标注分类
     * 
     * @param annotationCategories 标注分类
     * @return 结果
     */
    int updateAnnotationCategories(AnnotationCategories annotationCategories);

    /**
     * 删除标注分类
     * 
     * @param categoryId 标注分类主键
     * @return 结果
     */
    int deleteAnnotationCategoriesByCategoryId(Long categoryId);

    /**
     * 批量删除标注分类
     * 
     * @param categoryIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationCategoriesByCategoryIds(Long[] categoryIds);
}

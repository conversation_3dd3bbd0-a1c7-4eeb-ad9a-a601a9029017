// 这是一个简化的nlohmann/json头文件，仅包含基本功能
// 完整版本请从 https://github.com/nlohmann/json 下载

#ifndef NLOHMANN_JSON_HPP
#define NLOHMANN_JSON_HPP

#include <string>
#include <vector>
#include <map>
#include <iostream>
#include <sstream>

namespace nlohmann {

class json {
public:
    enum value_t {
        null,
        object,
        array,
        string,
        boolean,
        number_integer,
        number_unsigned,
        number_float
    };

private:
    value_t m_type = value_t::null;
    union {
        std::map<std::string, json>* m_object;
        std::vector<json>* m_array;
        std::string* m_string;
        bool m_boolean;
        int64_t m_integer;
        uint64_t m_unsigned;
        double m_float;
    };

public:
    json() : m_type(value_t::null) {}
    
    json(const std::string& str) : m_type(value_t::string) {
        m_string = new std::string(str);
    }
    
    json(const char* str) : m_type(value_t::string) {
        m_string = new std::string(str);
    }
    
    json(bool b) : m_type(value_t::boolean), m_boolean(b) {}
    
    json(int i) : m_type(value_t::number_integer), m_integer(i) {}
    
    json(double d) : m_type(value_t::number_float), m_float(d) {}
    
    ~json() {
        clear();
    }
    
    json(const json& other) {
        copy_from(other);
    }
    
    json& operator=(const json& other) {
        if (this != &other) {
            clear();
            copy_from(other);
        }
        return *this;
    }
    
    static json parse(const std::string& str) {
        json result;
        // 简化的解析器 - 仅支持数组格式 [[x,y], [x,y], ...]
        if (str.empty() || str[0] != '[') {
            return result;
        }
        
        result.m_type = value_t::array;
        result.m_array = new std::vector<json>();
        
        size_t pos = 1; // 跳过开始的 '['
        while (pos < str.length() && str[pos] != ']') {
            if (str[pos] == '[') {
                // 解析内部数组
                size_t end = str.find(']', pos);
                if (end != std::string::npos) {
                    std::string inner = str.substr(pos + 1, end - pos - 1);
                    json inner_array;
                    inner_array.m_type = value_t::array;
                    inner_array.m_array = new std::vector<json>();
                    
                    // 解析逗号分隔的数字
                    std::stringstream ss(inner);
                    std::string item;
                    while (std::getline(ss, item, ',')) {
                        // 去除空格
                        item.erase(0, item.find_first_not_of(" \t"));
                        item.erase(item.find_last_not_of(" \t") + 1);
                        if (!item.empty()) {
                            inner_array.m_array->push_back(json(std::stod(item)));
                        }
                    }
                    
                    result.m_array->push_back(inner_array);
                    pos = end + 1;
                }
            }
            pos++;
        }
        
        return result;
    }
    
    bool is_array() const { return m_type == value_t::array; }
    bool empty() const { 
        return m_type == value_t::null || 
               (m_type == value_t::array && m_array->empty());
    }
    
    size_t size() const {
        if (m_type == value_t::array) return m_array->size();
        return 0;
    }
    
    json& operator[](size_t index) {
        if (m_type != value_t::array) {
            clear();
            m_type = value_t::array;
            m_array = new std::vector<json>();
        }
        if (index >= m_array->size()) {
            m_array->resize(index + 1);
        }
        return (*m_array)[index];
    }
    
    const json& operator[](size_t index) const {
        static json null_json;
        if (m_type == value_t::array && index < m_array->size()) {
            return (*m_array)[index];
        }
        return null_json;
    }
    
    template<typename T>
    T get() const {
        if constexpr (std::is_same_v<T, float>) {
            if (m_type == value_t::number_float) return static_cast<float>(m_float);
            if (m_type == value_t::number_integer) return static_cast<float>(m_integer);
        }
        if constexpr (std::is_same_v<T, double>) {
            if (m_type == value_t::number_float) return m_float;
            if (m_type == value_t::number_integer) return static_cast<double>(m_integer);
        }
        return T{};
    }
    
    // 迭代器支持
    class iterator {
        std::vector<json>::iterator it;
    public:
        iterator(std::vector<json>::iterator i) : it(i) {}
        json& operator*() { return *it; }
        iterator& operator++() { ++it; return *this; }
        bool operator!=(const iterator& other) const { return it != other.it; }
    };
    
    iterator begin() {
        if (m_type == value_t::array) return iterator(m_array->begin());
        static std::vector<json> empty_vec;
        return iterator(empty_vec.begin());
    }
    
    iterator end() {
        if (m_type == value_t::array) return iterator(m_array->end());
        static std::vector<json> empty_vec;
        return iterator(empty_vec.end());
    }

private:
    void clear() {
        switch (m_type) {
            case value_t::object:
                delete m_object;
                break;
            case value_t::array:
                delete m_array;
                break;
            case value_t::string:
                delete m_string;
                break;
            default:
                break;
        }
        m_type = value_t::null;
    }
    
    void copy_from(const json& other) {
        m_type = other.m_type;
        switch (m_type) {
            case value_t::object:
                m_object = new std::map<std::string, json>(*other.m_object);
                break;
            case value_t::array:
                m_array = new std::vector<json>(*other.m_array);
                break;
            case value_t::string:
                m_string = new std::string(*other.m_string);
                break;
            case value_t::boolean:
                m_boolean = other.m_boolean;
                break;
            case value_t::number_integer:
                m_integer = other.m_integer;
                break;
            case value_t::number_unsigned:
                m_unsigned = other.m_unsigned;
                break;
            case value_t::number_float:
                m_float = other.m_float;
                break;
            default:
                break;
        }
    }
};

} // namespace nlohmann

#endif // NLOHMANN_JSON_HPP

CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj: \
 C:\work\code\java\data-annotation-platform\native\src\image_crop.cpp \
 C:/work/code/java/data-annotation-platform/native/include/image_crop.h \
 C:/msys64/mingw64/include/c++/15.1.0/string \
 C:/msys64/mingw64/include/c++/15.1.0/bits/requires_hosted.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/c++config.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/os_defines.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/cpu_defines.h \
 C:/msys64/mingw64/include/c++/15.1.0/pstl/pstl_config.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stringfwd.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/memoryfwd.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/char_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/postypes.h \
 C:/msys64/mingw64/include/c++/15.1.0/cwchar \
 C:/msys64/mingw64/include/wchar.h C:/msys64/mingw64/include/corecrt.h \
 C:/msys64/mingw64/include/_mingw.h \
 C:/msys64/mingw64/include/_mingw_mac.h \
 C:/msys64/mingw64/include/_mingw_secapi.h \
 C:/msys64/mingw64/include/vadefs.h \
 C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 C:/msys64/mingw64/include/corecrt_stdio_config.h \
 C:/msys64/mingw64/include/corecrt_wstdlib.h \
 C:/msys64/mingw64/include/corecrt_wctype.h \
 C:/msys64/mingw64/include/_mingw_off_t.h \
 C:/msys64/mingw64/include/_mingw_stat64.h \
 C:/msys64/mingw64/include/swprintf.inl \
 C:/msys64/mingw64/include/sec_api/wchar_s.h \
 C:/msys64/mingw64/include/c++/15.1.0/type_traits \
 C:/msys64/mingw64/include/c++/15.1.0/bits/version.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/allocator.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/c++allocator.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/new_allocator.h \
 C:/msys64/mingw64/include/c++/15.1.0/new \
 C:/msys64/mingw64/include/c++/15.1.0/bits/exception.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/functexcept.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/exception_defines.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/move.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/cpp_type_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/localefwd.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/c++locale.h \
 C:/msys64/mingw64/include/c++/15.1.0/clocale \
 C:/msys64/mingw64/include/locale.h C:/msys64/mingw64/include/crtdefs.h \
 C:/msys64/mingw64/include/stdio.h \
 C:/msys64/mingw64/include/sec_api/stdio_s.h \
 C:/msys64/mingw64/include/c++/15.1.0/iosfwd \
 C:/msys64/mingw64/include/c++/15.1.0/cctype \
 C:/msys64/mingw64/include/ctype.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/ostream_insert.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/cxxabi_forced.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_iterator_base_funcs.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/concept_check.h \
 C:/msys64/mingw64/include/c++/15.1.0/debug/assertions.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_iterator_base_types.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_iterator.h \
 C:/msys64/mingw64/include/c++/15.1.0/ext/type_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/ptr_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_function.h \
 C:/msys64/mingw64/include/c++/15.1.0/backward/binders.h \
 C:/msys64/mingw64/include/c++/15.1.0/ext/numeric_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_algobase.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_pair.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/utility.h \
 C:/msys64/mingw64/include/c++/15.1.0/debug/debug.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/predefined_ops.h \
 C:/msys64/mingw64/include/c++/15.1.0/bit \
 C:/msys64/mingw64/include/c++/15.1.0/concepts \
 C:/msys64/mingw64/include/c++/15.1.0/bits/refwrap.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/invoke.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/range_access.h \
 C:/msys64/mingw64/include/c++/15.1.0/initializer_list \
 C:/msys64/mingw64/include/c++/15.1.0/bits/basic_string.h \
 C:/msys64/mingw64/include/c++/15.1.0/ext/alloc_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/alloc_traits.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_construct.h \
 C:/msys64/mingw64/include/c++/15.1.0/string_view \
 C:/msys64/mingw64/include/c++/15.1.0/bits/functional_hash.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/hash_bytes.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/string_view.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/ext/string_conversions.h \
 C:/msys64/mingw64/include/c++/15.1.0/cstdlib \
 C:/msys64/mingw64/include/stdlib.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h \
 C:/msys64/mingw64/include/limits.h \
 C:/msys64/mingw64/include/sec_api/stdlib_s.h \
 C:/msys64/mingw64/include/c++/15.1.0/stdlib.h \
 C:/msys64/mingw64/include/malloc.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h \
 C:/msys64/mingw64/include/errno.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/std_abs.h \
 C:/msys64/mingw64/include/c++/15.1.0/cstdio \
 C:/msys64/mingw64/include/c++/15.1.0/cerrno \
 C:/msys64/mingw64/include/c++/15.1.0/bits/charconv.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/basic_string.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/memory_resource.h \
 C:/msys64/mingw64/include/c++/15.1.0/cstddef \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h \
 C:/msys64/mingw64/include/stddef.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/uses_allocator.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/uses_allocator_args.h \
 C:/msys64/mingw64/include/c++/15.1.0/tuple \
 C:/msys64/mingw64/include/c++/15.1.0/vector \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_uninitialized.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_vector.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_bvector.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/vector.tcc \
 C:/msys64/mingw64/include/opencv4/opencv2/opencv.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/opencv_modules.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cvdef.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/version.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/limits \
 C:/msys64/mingw64/include/opencv4/opencv2/core/hal/interface.h \
 C:/msys64/mingw64/include/c++/15.1.0/cstdint \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdint.h \
 C:/msys64/mingw64/include/stdint.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cv_cpu_dispatch.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/emmintrin.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xmmintrin.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mmintrin.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/base.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/climits \
 C:/msys64/mingw64/include/c++/15.1.0/algorithm \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_algo.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/algorithmfwd.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_heap.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/uniform_int_dist.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_tempbuf.h \
 C:/msys64/mingw64/include/c++/15.1.0/pstl/glue_algorithm_defs.h \
 C:/msys64/mingw64/include/c++/15.1.0/pstl/execution_defs.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cvstd.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/cstring \
 C:/msys64/mingw64/include/string.h \
 C:/msys64/mingw64/include/sec_api/string_s.h \
 C:/msys64/mingw64/include/c++/15.1.0/utility \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_relops.h \
 C:/msys64/mingw64/include/c++/15.1.0/cmath \
 C:/msys64/mingw64/include/math.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/specfun.h \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/gamma.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/special_function_util.h \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/bessel_function.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/beta_function.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/ell_integral.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/exp_integral.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/hypergeometric.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/legendre_function.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/modified_bessel_func.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/poly_hermite.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/poly_laguerre.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/tr1/riemann_zeta.tcc \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cvstd_wrapper.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/memory \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_raw_storage_iter.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/align.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/unique_ptr.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/shared_ptr.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/shared_ptr_base.h \
 C:/msys64/mingw64/include/c++/15.1.0/typeinfo \
 C:/msys64/mingw64/include/c++/15.1.0/bits/allocated_ptr.h \
 C:/msys64/mingw64/include/c++/15.1.0/ext/aligned_buffer.h \
 C:/msys64/mingw64/include/c++/15.1.0/ext/atomicity.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/gthr.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/gthr-default.h \
 C:/msys64/mingw64/include/pthread.h \
 C:/msys64/mingw64/include/sys/types.h \
 C:/msys64/mingw64/include/process.h \
 C:/msys64/mingw64/include/corecrt_startup.h \
 C:/msys64/mingw64/include/signal.h \
 C:/msys64/mingw64/include/pthread_signal.h \
 C:/msys64/mingw64/include/time.h C:/msys64/mingw64/include/sys/timeb.h \
 C:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
 C:/msys64/mingw64/include/_timeval.h \
 C:/msys64/mingw64/include/pthread_time.h \
 C:/msys64/mingw64/include/pthread_compat.h \
 C:/msys64/mingw64/include/sched.h \
 C:/msys64/mingw64/include/pthread_unistd.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/atomic_word.h \
 C:/msys64/mingw64/include/c++/15.1.0/ext/concurrence.h \
 C:/msys64/mingw64/include/c++/15.1.0/exception \
 C:/msys64/mingw64/include/c++/15.1.0/bits/exception_ptr.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/cxxabi_init_exception.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/nested_exception.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/shared_ptr_atomic.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/atomic_base.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/atomic_lockfree_defines.h \
 C:/msys64/mingw64/include/c++/15.1.0/backward/auto_ptr.h \
 C:/msys64/mingw64/include/c++/15.1.0/pstl/glue_memory_defs.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/neon_utils.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/vsx_utils.hpp \
 C:/msys64/mingw64/include/assert.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/check.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/traits.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/matx.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/saturate.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/fast_math.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/matx.inl.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/types.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/cfloat \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/float.h \
 C:/msys64/mingw64/include/float.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/mat.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/bufferpool.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/array \
 C:/msys64/mingw64/include/c++/15.1.0/compare \
 C:/msys64/mingw64/include/opencv4/opencv2/core/mat.inl.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/persistence.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/operations.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cvstd.inl.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/complex \
 C:/msys64/mingw64/include/c++/15.1.0/sstream \
 C:/msys64/mingw64/include/c++/15.1.0/istream \
 C:/msys64/mingw64/include/c++/15.1.0/ios \
 C:/msys64/mingw64/include/c++/15.1.0/bits/ios_base.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_classes.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_classes.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/system_error \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/error_constants.h \
 C:/msys64/mingw64/include/c++/15.1.0/stdexcept \
 C:/msys64/mingw64/include/c++/15.1.0/streambuf \
 C:/msys64/mingw64/include/c++/15.1.0/bits/streambuf.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/basic_ios.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_facets.h \
 C:/msys64/mingw64/include/c++/15.1.0/cwctype \
 C:/msys64/mingw64/include/wctype.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/ctype_base.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/streambuf_iterator.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/ctype_inline.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_facets.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/basic_ios.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/ostream \
 C:/msys64/mingw64/include/c++/15.1.0/bits/ostream.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/ostream.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/istream.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/sstream.tcc \
 C:/msys64/mingw64/include/opencv4/opencv2/core/utility.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/functional \
 C:/msys64/mingw64/include/c++/15.1.0/bits/std_function.h \
 C:/msys64/mingw64/include/c++/15.1.0/unordered_map \
 C:/msys64/mingw64/include/c++/15.1.0/bits/unordered_map.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/hashtable.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/hashtable_policy.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/enable_special_members.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/node_handle.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/erase_if.h \
 C:/msys64/mingw64/include/c++/15.1.0/mutex \
 C:/msys64/mingw64/include/c++/15.1.0/bits/chrono.h \
 C:/msys64/mingw64/include/c++/15.1.0/ratio \
 C:/msys64/mingw64/include/c++/15.1.0/ctime \
 C:/msys64/mingw64/include/c++/15.1.0/bits/parse_numbers.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/std_mutex.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/unique_lock.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/optim.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/ovx.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cvdef.h \
 C:/msys64/mingw64/include/opencv4/opencv2/calib3d.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/features2d.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/miniflann.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/defines.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/config.h \
 C:/msys64/mingw64/include/opencv4/opencv2/core/affine.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/utils/logger.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/iostream \
 C:/msys64/mingw64/include/opencv4/opencv2/core/utils/logger.defines.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/utils/logtag.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/dnn.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/async.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/chrono \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/version.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/dict.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/map \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_tree.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_map.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_multimap.h \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/layer.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/dnn.inl.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/utils/inference_engine.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/dnn/dnn.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/flann.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/flann_base.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/general.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/matrix.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/params.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/any.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/defines.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/saving.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/nn_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/result_set.h \
 C:/msys64/mingw64/include/c++/15.1.0/set \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_set.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_multiset.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/all_indices.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/kdtree_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/dynamic_bitset.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/dist.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/heap.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/allocator.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/random.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/kdtree_single_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/kmeans_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/logger.h \
 C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdarg.h \
 C:/msys64/mingw64/include/stdarg.h \
 C:/msys64/mingw64/include/_mingw_stdarg.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/composite_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/linear_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/hierarchical_clustering_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/lsh_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/lsh_table.h \
 C:/msys64/mingw64/include/c++/15.1.0/iomanip \
 C:/msys64/mingw64/include/c++/15.1.0/locale \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_facets_nonio.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/time_members.h \
 C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/messages_members.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/codecvt.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_facets_nonio.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/locale_conv.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/quoted_string.h \
 C:/msys64/mingw64/include/c++/15.1.0/math.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/autotuned_index.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/ground_truth.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/index_testing.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/timer.h \
 C:/msys64/mingw64/include/opencv4/opencv2/flann/sampling.h \
 C:/msys64/mingw64/include/opencv4/opencv2/highgui.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/imgcodecs.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/videoio.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/imgproc.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/imgproc/segmentation.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/ml.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/ml/ml.inl.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/aruco_detector.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/aruco_dictionary.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/aruco_board.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/graphical_code_detector.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/detection_based_tracker.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/face.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/charuco_detector.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/objdetect/barcode.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/photo.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/warpers.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/warpers.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cuda.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cuda_types.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/core/cuda.inl.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/warpers_inl.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/warpers.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/matchers.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/motion_estimators.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/matchers.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/util.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/list \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_list.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/list.tcc \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/util_inl.hpp \
 C:/msys64/mingw64/include/c++/15.1.0/queue \
 C:/msys64/mingw64/include/c++/15.1.0/deque \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_deque.h \
 C:/msys64/mingw64/include/c++/15.1.0/bits/deque.tcc \
 C:/msys64/mingw64/include/c++/15.1.0/bits/stl_queue.h \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/camera.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/exposure_compensate.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/seam_finders.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/blenders.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/stitching/detail/camera.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/video.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/video/tracking.hpp \
 C:/msys64/mingw64/include/opencv4/opencv2/video/background_segm.hpp \
 C:/work/code/java/data-annotation-platform/native/third_party/nlohmann/json.hpp

package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationProjectsMapper;
import com.ylzx.annotation.domain.AnnotationProjects;
import com.ylzx.annotation.service.AnnotationProjectsService;

/**
 * 标注项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationProjectsServiceImpl extends ServiceImpl<AnnotationProjectsMapper,AnnotationProjects> implements AnnotationProjectsService
{
    @Autowired
    private AnnotationProjectsMapper annotationProjectsMapper;

    /**
     * 查询标注项目
     * 
     * @param projectId 标注项目主键
     * @return 标注项目
     */
    @Override
    public AnnotationProjects selectAnnotationProjectsByProjectId(Long projectId)
    {
        return annotationProjectsMapper.selectAnnotationProjectsByProjectId(projectId);
    }

    /**
     * 查询标注项目列表
     * 
     * @param annotationProjects 标注项目
     * @return 标注项目集合
     */
    @Override
    public List<AnnotationProjects> selectAnnotationProjectsList(AnnotationProjects annotationProjects)
    {
        return annotationProjectsMapper.selectAnnotationProjectsList(annotationProjects);
    }

    /**
     * 新增标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    @Override
    public int insertAnnotationProjects(AnnotationProjects annotationProjects)
    {
        return annotationProjectsMapper.insertAnnotationProjects(annotationProjects);
    }

    /**
     * 修改标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    @Override
    public int updateAnnotationProjects(AnnotationProjects annotationProjects)
    {
        return annotationProjectsMapper.updateAnnotationProjects(annotationProjects);
    }

    /**
     * 批量删除标注项目
     * 
     * @param projectIds 需要删除的标注项目主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectsByProjectIds(Long[] projectIds)
    {
        return annotationProjectsMapper.deleteAnnotationProjectsByProjectIds(projectIds);
    }

    /**
     * 删除标注项目信息
     * 
     * @param projectId 标注项目主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectsByProjectId(Long projectId)
    {
        return annotationProjectsMapper.deleteAnnotationProjectsByProjectId(projectId);
    }
}

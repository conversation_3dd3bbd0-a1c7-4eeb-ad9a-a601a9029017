package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationProjects;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationProjectsService extends IService<AnnotationProjects>
{
    /**
     * 查询标注项目
     * 
     * @param projectId 标注项目主键
     * @return 标注项目
     */
    AnnotationProjects selectAnnotationProjectsByProjectId(Long projectId);

    /**
     * 查询标注项目列表
     * 
     * @param annotationProjects 标注项目
     * @return 标注项目集合
     */
    List<AnnotationProjects> selectAnnotationProjectsList(AnnotationProjects annotationProjects);

    /**
     * 新增标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    int insertAnnotationProjects(AnnotationProjects annotationProjects);

    /**
     * 修改标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    int updateAnnotationProjects(AnnotationProjects annotationProjects);

    /**
     * 批量删除标注项目
     * 
     * @param projectIds 需要删除的标注项目主键集合
     * @return 结果
     */
    int deleteAnnotationProjectsByProjectIds(Long[] projectIds);

    /**
     * 删除标注项目信息
     * 
     * @param projectId 标注项目主键
     * @return 结果
     */
    int deleteAnnotationProjectsByProjectId(Long projectId);
}

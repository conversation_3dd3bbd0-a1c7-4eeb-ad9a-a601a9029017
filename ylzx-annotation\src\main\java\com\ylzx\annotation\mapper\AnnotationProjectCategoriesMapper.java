package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationProjectCategories;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标注项目分类关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Mapper
public interface AnnotationProjectCategoriesMapper extends BaseMapper<AnnotationProjectCategories>
{
    /**
     * 查询项目关联的分类列表
     * 
     * @param projectId 项目ID
     * @return 分类ID列表
     */
    List<Long> selectCategoryIdsByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询分类关联的项目列表
     * 
     * @param categoryId 分类ID
     * @return 项目ID列表
     */
    List<Long> selectProjectIdsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 批量插入项目分类关联
     * 
     * @param relations 关联关系列表
     * @return 插入数量
     */
    int insertBatch(@Param("list") List<AnnotationProjectCategories> relations);

    /**
     * 删除项目的所有分类关联
     * 
     * @param projectId 项目ID
     * @return 删除数量
     */
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 删除分类的所有项目关联
     * 
     * @param categoryId 分类ID
     * @return 删除数量
     */
    int deleteByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 检查项目分类关联是否存在
     * 
     * @param projectId 项目ID
     * @param categoryId 分类ID
     * @return 是否存在
     */
    boolean existsRelation(@Param("projectId") Long projectId, @Param("categoryId") Long categoryId);
}

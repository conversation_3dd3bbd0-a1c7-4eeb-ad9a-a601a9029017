package com.ylzx.annotation.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.Collections;
import java.util.Date;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.vo.ImageWithAnnotationsVo;
import com.ylzx.annotation.service.AnnotationCategoriesImagesService;
import com.ylzx.annotation.service.AnnotationCategoriesService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.service.AnnotationAnnotationsService;
import com.ylzx.common.utils.SecurityUtils;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.annotation.mapper.AnnotationImagesMapper;
import com.ylzx.annotation.domain.AnnotationImages;
import lombok.extern.slf4j.Slf4j;

/**
 * 标注项目图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Slf4j
@Service
public class AnnotationCategoriesImagesServiceImpl extends ServiceImpl<AnnotationCategoriesImagesMapper, AnnotationCategoriesImages> implements AnnotationCategoriesImagesService
{
    @Autowired
    private AnnotationCategoriesImagesMapper annotationCategoriesImagesMapper;

    @Autowired
    private AnnotationCategoriesService annotationCategoriesService;

    @Autowired
    private AnnotationImagesMapper annotationImagesMapper;

    @Autowired
    private AnnotationImagesService annotationImagesService;

    @Autowired
    private AnnotationAnnotationsService annotationAnnotationsService;
    
    // 创建固定大小的线程池，一个用于生产者，一个用于消费者
    private final ExecutorService executorService = Executors.newFixedThreadPool(2);

    // 定义任务超时时间，例如30分钟
    private static final long TIMEOUT_IN_MILLIS = 30 * 60 * 1000;

    /**
     * 查询标注项目图片
     * 
     * @param categoryId 标注项目图片主键
     * @return 标注项目图片
     */
    @Override
    public AnnotationCategoriesImages selectAnnotationCategoriesImagesByCategoryId(Long categoryId)
    {
        return annotationCategoriesImagesMapper.selectAnnotationCategoriesImagesByCategoryId(categoryId);
    }

    /**
     * 查询标注项目图片列表
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 标注项目图片集合
     */
    @Override
    public List<AnnotationCategoriesImages> selectAnnotationCategoriesImagesList(AnnotationCategoriesImages annotationCategoriesImages)
    {
        return annotationCategoriesImagesMapper.selectAnnotationCategoriesImagesList(annotationCategoriesImages);
    }

    /**
     * 新增标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    @Override
    public int insertAnnotationCategoriesImages(AnnotationCategoriesImages annotationCategoriesImages)
    {
        return annotationCategoriesImagesMapper.insertAnnotationCategoriesImages(annotationCategoriesImages);
    }

    /**
     * 修改标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    @Override
    public int updateAnnotationCategoriesImages(AnnotationCategoriesImages annotationCategoriesImages)
    {
        return annotationCategoriesImagesMapper.updateAnnotationCategoriesImages(annotationCategoriesImages);
    }

    /**
     * 批量删除标注项目图片
     * 
     * @param categoryIds 需要删除的标注项目图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationCategoriesImagesByCategoryIds(Long[] categoryIds)
    {
        return annotationCategoriesImagesMapper.deleteAnnotationCategoriesImagesByCategoryIds(categoryIds);
    }

    /**
     * 删除标注项目图片信息
     * 
     * @param categoryId 标注项目图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationCategoriesImagesByCategoryId(Long categoryId)
    {
        return annotationCategoriesImagesMapper.deleteAnnotationCategoriesImagesByCategoryId(categoryId);
    }
    
    /**
     * 关联图片源中的所有图片到指定的标注分类中
     * 使用生产者-消费者模式：一个线程生成数据，一个线程批量插入
     *
     * @param sourceId 图片来源ID
     * @param categoryId 标注分类ID
     * @return 关联的图片数量
     */
    @Override
    public int associateSourceImagesToCategory(Long sourceId, Long categoryId) {
        // 1. 检查分类是否存在
        AnnotationCategories category = annotationCategoriesService.selectAnnotationCategoriesByCategoryId(categoryId);
        if (category == null) {
            throw new IllegalArgumentException("标注分类不存在，分类ID: " + categoryId);
        }
        // 2. 获取指定图片源的所有图片ID
        AnnotationImages imageQuery = new AnnotationImages();
        imageQuery.setSourceId(sourceId);
        List<AnnotationImages> images = annotationImagesMapper.selectAnnotationImagesList(imageQuery);
        if (images == null || images.isEmpty()) {
            log.warn("指定的图片源没有图片，源ID: {}", sourceId);
            return 0;
        }
        
        log.info("开始关联图片到分类，总图片数: {}, 分类ID: {}", images.size(), categoryId);

        // 使用有界队列防止内存溢出，容量可以根据内存和生产者-消费者速度调整
        // 将容量增加到1000，为消费者提供更大的缓冲，减少生产者阻塞的可能性
        final BlockingQueue<AnnotationCategoriesImages> queue = new LinkedBlockingQueue<>(1000);
        final AtomicInteger totalInserted = new AtomicInteger(0);
        final int batchSize = 100;
        
        // 生产者任务：查询到的图片，构建成关联对象，放入队列
        CompletableFuture<Void> producerFuture = CompletableFuture.runAsync(() -> {
            try {
                log.info("生产者任务已启动。");
                final java.time.LocalDateTime now = java.time.LocalDateTime.now();
                final String userName = SecurityUtils.getUsername();
                for (AnnotationImages image : images) {
                    AnnotationCategoriesImages relation = new AnnotationCategoriesImages();
                    relation.setCategoryId(categoryId);
                    relation.setImageId(image.getImageId());
                    relation.setStatus(com.ylzx.annotation.domain.enums.AnnotationStatus.NOT_ANNOTATED);
                    relation.setCreateTime(now);
                    relation.setUpdateTime(now);
                    relation.setCreateBy(userName);
                    relation.setUpdateBy(userName);
                    // put方法在队列满时会阻塞，防止生产过快
                    queue.put(relation);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("生产者任务被中断。", e);
            } finally {
                log.info("生产者任务已结束。");
            }
        }, executorService);

        // 消费者任务：从队列中取出对象，凑够一批后批量插入数据库
        CompletableFuture<Void> consumerFuture = CompletableFuture.runAsync(() -> {
            log.info("消费者任务已启动。");
            List<AnnotationCategoriesImages> batchList = new ArrayList<>(batchSize);
            try {
                // 循环直到生产者完成并且队列为空
                while (!producerFuture.isDone() || !queue.isEmpty()) {
                    // 从队列中获取一个元素，最多等待100ms
                    AnnotationCategoriesImages item = queue.poll(100, TimeUnit.MILLISECONDS);
                    if (item != null) {
                        batchList.add(item);
                    }
                    // 当达到批处理大小，或者生产者已完成且列表中有剩余项目时，执行插入
                    if (batchList.size() >= batchSize || (producerFuture.isDone() && !batchList.isEmpty())) {
                        log.debug("执行批量插入，数量: {}", batchList.size());
                        int inserted = annotationCategoriesImagesMapper.insertBatch(batchList);
                        totalInserted.addAndGet(inserted);
                        log.debug("批量插入完成，成功插入: {}", inserted);
                        batchList.clear();
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("消费者任务被中断。", e);
            }
            log.info("消费者任务已结束。");
        }, executorService);
        try {
            // 等待两个任务都执行完毕
            CompletableFuture.allOf(producerFuture, consumerFuture).join();
            log.info("图片关联处理完成，共关联图片: {}", totalInserted.get());
            return totalInserted.get();
        } catch (Exception e) {
            log.error("关联图片到分类时发生异常", e);
            // 在实际应用中，可能需要更复杂的异常处理和回滚逻辑
            throw new RuntimeException("图片关联处理失败", e);
        }
    }

    /**
     * 为用户领取指定数量的标注任务。
     *
     * @param categoryId 类目ID
     * @param userId     用户ID
     * @param limit      需要领取的数量
     * @return 领取到的任务列表，如果没有可领取的则返回空列表。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AnnotationCategoriesImages> claimImagesForAnnotation(Long categoryId, Long userId, int limit) {
        if (userId == null) {
            userId = SecurityUtils.getUserId();
        }
        // 步骤1: 使用数据库行锁查询并锁定可分配的任务ID。
        // "SKIP LOCKED"确保在高并发时，不同用户不会因等待同一个锁而阻塞，而是直接跳过被锁的行，去获取其他可用行。
        List<Long> availableIds = annotationCategoriesImagesMapper.findAvailableImageIds(categoryId, com.ylzx.annotation.domain.enums.AnnotationStatus.NOT_ANNOTATED, limit);

        if (availableIds == null || availableIds.isEmpty()) {
            return Collections.emptyList(); // 没有可领取的任务
        }
        // 步骤2: 更新这些任务的状态，将其正式分配给当前用户。
        Date claimTime = new Date();
        annotationCategoriesImagesMapper.assignImagesToUser(availableIds, userId, claimTime, com.ylzx.annotation.domain.enums.AnnotationStatus.IN_PROGRESS);
        // 步骤3: 查询并返回分配成功的完整任务信息给前端。
        return annotationCategoriesImagesMapper.findByIds(availableIds);
    }

    @Override
    public List<AnnotationCategoriesImages> getMyUnfinishedTasks(Long categoryId) {
        Long userId = SecurityUtils.getUserId();
        return annotationCategoriesImagesMapper.findUserTasksByStatus(categoryId, userId, com.ylzx.annotation.domain.enums.AnnotationStatus.IN_PROGRESS);
    }

    @Override
    public List<AnnotationCategoriesImages> getMyTasksByStatuses(Long categoryId, List<com.ylzx.annotation.domain.enums.AnnotationStatus> statuses) {
        Long userId = SecurityUtils.getUserId();
        if (statuses == null || statuses.isEmpty()) {
            return Collections.emptyList();
        }
        if (categoryId == null) {
            // 查询所有分类下的任务
            return annotationCategoriesImagesMapper.findAllUserTasksByStatuses(userId, statuses);
        } else {
            // 查询指定分类下的任务
            return annotationCategoriesImagesMapper.findUserTasksByStatuses(categoryId, userId, statuses);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int releaseMyTasks(Long categoryId) {
        Long userId = SecurityUtils.getUserId();
        log.info("用户 [userId={}] 正在释放其在分类 [categoryId={}] 下的未完成任务。", userId, categoryId);
        // 查找用户所有"进行中"的任务
        List<AnnotationCategoriesImages> tasksToRelease = getMyUnfinishedTasks(categoryId);
        if (tasksToRelease.isEmpty()) {
            log.info("用户 [userId={}] 在分类 [categoryId={}] 下没有需要释放的任务。", userId, categoryId);
            return 0;
        }
        List<Long> taskIdsToRelease = tasksToRelease.stream()
                .map(AnnotationCategoriesImages::getId)
                .collect(Collectors.toList());

        int releasedCount = annotationCategoriesImagesMapper.releaseTasksByIds(
            taskIdsToRelease,
            com.ylzx.annotation.domain.enums.AnnotationStatus.NOT_ANNOTATED
        );
        log.info("用户 [userId={}] 成功释放了 {} 个任务。", userId, releasedCount);
        return releasedCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int releaseMyTasksOptional(Long categoryId) {
        Long userId = SecurityUtils.getUserId();
        if (categoryId != null) {
            log.info("用户 [userId={}] 正在释放其在分类 [categoryId={}] 下的未完成任务。", userId, categoryId);
        } else {
            log.info("用户 [userId={}] 正在释放其在所有分类下的未完成任务。", userId);
        }
        // 查找用户所有"进行中"的任务，支持可选的categoryId
        List<com.ylzx.annotation.domain.enums.AnnotationStatus> statusesToRelease =
            Arrays.asList(com.ylzx.annotation.domain.enums.AnnotationStatus.IN_PROGRESS);
        List<AnnotationCategoriesImages> tasksToRelease = getMyTasksByStatuses(categoryId, statusesToRelease);
        if (tasksToRelease.isEmpty()) {
            if (categoryId != null) {
                log.info("用户 [userId={}] 在分类 [categoryId={}] 下没有需要释放的任务。", userId, categoryId);
            } else {
                log.info("用户 [userId={}] 在所有分类下没有需要释放的任务。", userId);
            }
            return 0;
        }
        List<Long> taskIdsToRelease = tasksToRelease.stream()
                .map(AnnotationCategoriesImages::getId)
                .collect(Collectors.toList());

        int releasedCount = annotationCategoriesImagesMapper.releaseTasksByIds(
            taskIdsToRelease,
            com.ylzx.annotation.domain.enums.AnnotationStatus.NOT_ANNOTATED
        );
        if (categoryId != null) {
            log.info("用户 [userId={}] 成功释放了分类 [categoryId={}] 下的 {} 个任务。", userId, categoryId, releasedCount);
        } else {
            log.info("用户 [userId={}] 成功释放了所有分类下的 {} 个任务。", userId, releasedCount);
        }
        return releasedCount;
    }

    /**
     * 【定时任务】 释放超时的标注任务
     * 默认每5分钟执行一次，检查并释放超过30分钟还未完成的【标注中】任务。
     * 您需要确保在启动类上有 @EnableScheduling 注解。
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void releaseExpiredTasks() {
        log.info("开始执行[超时标注任务]释放调度...");
        // 计算出超时的时间点，早于这个时间点的任务都算超时
        Date timeoutThreshold = new Date(System.currentTimeMillis() - TIMEOUT_IN_MILLIS);
        int releasedCount = annotationCategoriesImagesMapper.releaseTimedOutImages(
            timeoutThreshold,
            com.ylzx.annotation.domain.enums.AnnotationStatus.IN_PROGRESS, // 查找"标注中"的任务
            com.ylzx.annotation.domain.enums.AnnotationStatus.NOT_ANNOTATED  // 将其状态改回"未标注"
        );
        if (releasedCount > 0) {
            log.info("成功释放了 {} 个超时任务。", releasedCount);
        }
    }

    /**
     * 为用户领取指定数量的标注任务，返回图片和标注信息的组合。
     *
     * @param categoryId 类目ID
     * @param userId     用户ID
     * @param limit      需要领取的数量
     * @return 领取到的任务列表（包含图片和标注信息），如果没有可领取的则返回空列表。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImageWithAnnotationsVo> claimImagesForAnnotationWithDetails(Long categoryId, Long userId, int limit) {
        // 先领取任务
        List<AnnotationCategoriesImages> tasks = claimImagesForAnnotation(categoryId, userId, limit);

        if (tasks == null || tasks.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为详细信息
        return tasks.stream().map(task -> {
            // 获取图片信息
            AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(task.getImageId());

            // 获取该图片的标注列表
            AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
            queryAnnotation.setImageId(task.getImageId());
            queryAnnotation.setCategoryId(task.getCategoryId());
            List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);

            // 构建返回对象
            return ImageWithAnnotationsVo.builder()
                    .image(image)
                    .annotations(annotations != null ? annotations : Collections.emptyList())
                    .taskId(task.getId())
                    .annotatorId(task.getAnnotatorId())
                    .taskStatus(task.getStatus() != null ? task.getStatus().name() : null)
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 根据状态列表查询用户的任务，返回图片和标注信息的组合。
     * @param categoryId 类目ID，如果为null则查询所有类目
     * @param statuses 状态列表
     * @return 任务列表（包含图片和标注信息）
     */
    @Override
    public List<ImageWithAnnotationsVo> getMyTasksByStatusesWithDetails(Long categoryId, List<com.ylzx.annotation.domain.enums.AnnotationStatus> statuses) {
        // 先获取任务列表
        List<AnnotationCategoriesImages> tasks = getMyTasksByStatuses(categoryId, statuses);

        if (tasks == null || tasks.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为详细信息
        return tasks.stream().map(task -> {
            // 获取图片信息
            AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(task.getImageId());

            // 获取该图片的标注列表
            AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
            queryAnnotation.setImageId(task.getImageId());
            queryAnnotation.setCategoryId(task.getCategoryId());
            List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);

            // 构建返回对象
            return ImageWithAnnotationsVo.builder()
                    .image(image)
                    .annotations(annotations != null ? annotations : Collections.emptyList())
                    .taskId(task.getId())
                    .annotatorId(task.getAnnotatorId())
                    .taskStatus(task.getStatus() != null ? task.getStatus().name() : null)
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 获取单个任务的详细信息（图片+标注）
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    @Override
    public ImageWithAnnotationsVo getTaskWithDetails(Long taskId) {
        // 获取任务信息
        AnnotationCategoriesImages task = annotationCategoriesImagesMapper.selectById(taskId);
        if (task == null) {
            return null;
        }

        // 获取图片信息
        AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(task.getImageId());

        // 获取该图片的标注列表
        AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
        queryAnnotation.setImageId(task.getImageId());
        queryAnnotation.setCategoryId(task.getCategoryId());
        List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);

        // 构建返回对象
        return ImageWithAnnotationsVo.builder()
                .image(image)
                .annotations(annotations != null ? annotations : Collections.emptyList())
                .taskId(task.getId())
                .annotatorId(task.getAnnotatorId())
                .taskStatus(task.getStatus() != null ? task.getStatus().name() : null)
                .build();
    }
}

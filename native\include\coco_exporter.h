#ifndef COCO_EXPORTER_H
#define COCO_EXPORTER_H

#include "common.h"
#include <string>
#include <vector>

/**
 * COCO格式导出器
 */
class COCOExporter {
public:
    /**
     * 导出COCO格式数据集
     * 
     * @param config 导出配置
     * @param images 图像信息列表
     * @param categories 分类信息JSON
     * @return 导出结果
     */
    ExportResult exportDataset(const ExportConfig& config, 
                              const std::vector<ImageInfo>& images, 
                              const std::string& categories);

private:
    /**
     * 创建COCO格式的annotations.json文件
     */
    bool createAnnotationsFile(const std::string& outputPath,
                              const std::vector<ImageInfo>& images,
                              const std::string& categories,
                              DatasetType datasetType);
    
    /**
     * 处理单个图像
     */
    bool processImage(const ImageInfo& image, 
                     const ExportConfig& config,
                     const std::string& outputDir);
    
    /**
     * 解析标注数据
     */
    std::string parseAnnotationData(const std::string& annotationData);
};

#endif // COCO_EXPORTER_H

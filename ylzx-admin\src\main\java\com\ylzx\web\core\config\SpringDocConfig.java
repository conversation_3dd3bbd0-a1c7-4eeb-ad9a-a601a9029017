package com.ylzx.web.core.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SpringDoc OpenAPI 3 接口文档配置
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Configuration
public class SpringDocConfig {

    /** 项目名称 */
    @Value("${ylzx.name:YLZX}")
    private String projectName;

    /** 项目版本 */
    @Value("${ylzx.version:3.8.9}")
    private String projectVersion;

    /** 是否开启swagger */
    @Value("${springdoc.api-docs.enabled:true}")
    private boolean enabled;

    /**
     * 创建API文档
     */
    @Bean
    public OpenAPI createOpenAPI() {
        return new OpenAPI()
                .info(createApiInfo())
                .components(createComponents())
                .addSecurityItem(new SecurityRequirement().addList("Authorization"))
                .externalDocs(new ExternalDocumentation()
                        .description("YLZX 数据标注平台设计文档")
                        .url("https://github.com/ylzx/data-annotation-platform"));
    }

    /**
     * 系统管理模块API分组
     */
    @Bean
    public GroupedOpenApi systemApi() {
        return GroupedOpenApi.builder()
                .group("系统管理")
                .pathsToMatch("/system/**")
                .build();
    }

    /**
     * 标注管理模块API分组
     */
    @Bean
    public GroupedOpenApi annotationApi() {
        return GroupedOpenApi.builder()
                .group("标注管理")
                .pathsToMatch("/annotation/**")
                .build();
    }

    /**
     * 监控管理模块API分组
     */
    @Bean
    public GroupedOpenApi monitorApi() {
        return GroupedOpenApi.builder()
                .group("监控管理")
                .pathsToMatch("/monitor/**")
                .build();
    }

    /**
     * API信息
     */
    private Info createApiInfo() {
        return new Info()
                .title("YLZX 数据标注平台 API 文档")
                .description("用于数据标注平台的接口文档，包括标注管理、审核管理、用户管理等模块")
                .version("版本号: " + projectVersion)
                .contact(new Contact()
                        .name(projectName)
                        .email("<EMAIL>")
                        .url("https://www.ylzx.com"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }

    /**
     * 安全配置
     */
    private Components createComponents() {
        return new Components()
                .addSecuritySchemes("Authorization", 
                    new SecurityScheme()
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .description("请输入 JWT Token，格式为：Bearer {token}"));
    }

    /**
     * 测试模块API分组
     */
    @Bean
    public GroupedOpenApi testApi() {
        return GroupedOpenApi.builder()
                .group("测试接口")
                .pathsToMatch("/test/**")
                .build();
    }
}

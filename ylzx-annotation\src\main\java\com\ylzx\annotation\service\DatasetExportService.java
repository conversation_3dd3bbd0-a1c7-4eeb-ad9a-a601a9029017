package com.ylzx.annotation.service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import com.ylzx.annotation.domain.AnnotationExportJobs;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;

/**
 * 数据集导出服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface DatasetExportService
{
    /**
     * 导出配置验证结果
     */
    class ExportValidationResult {
        private boolean valid;
        private String message;
        private List<String> errors;
        
        public ExportValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public ExportValidationResult(boolean valid, String message, List<String> errors) {
            this.valid = valid;
            this.message = message;
            this.errors = errors;
        }
        
        // Getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
    }

    /**
     * 导出任务状态
     */
    enum ExportTaskStatus {
        PENDING("pending", "等待中"),
        RUNNING("running", "执行中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "失败"),
        CANCELLED("cancelled", "已取消");
        
        private final String code;
        private final String description;
        
        ExportTaskStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * 验证导出配置
     * 
     * @param config 项目配置
     * @param categoryIds 分类ID列表
     * @return 验证结果
     */
    ExportValidationResult validateExportConfig(AnnotationProjectConfigs config, List<Long> categoryIds);

    /**
     * 创建导出任务
     * 
     * @param projectId 项目ID
     * @param categoryIds 分类ID列表（可选，为空则导出所有分类）
     * @param incrementalExport 是否增量导出
     * @return 导出任务ID
     */
    Long createExportTask(Long projectId, List<Long> categoryIds, boolean incrementalExport);

    /**
     * 异步执行导出任务
     * 
     * @param jobId 任务ID
     * @return 异步任务结果
     */
    CompletableFuture<Boolean> executeExportTaskAsync(Long jobId);

    /**
     * 同步执行导出任务
     * 
     * @param jobId 任务ID
     * @return 是否成功
     */
    boolean executeExportTask(Long jobId);

    /**
     * 取消导出任务
     * 
     * @param jobId 任务ID
     * @return 是否成功取消
     */
    boolean cancelExportTask(Long jobId);

    /**
     * 查询导出任务状态
     * 
     * @param jobId 任务ID
     * @return 任务状态
     */
    ExportTaskStatus getExportTaskStatus(Long jobId);

    /**
     * 查询导出任务详情
     * 
     * @param jobId 任务ID
     * @return 任务详情
     */
    AnnotationExportJobs getExportTaskDetails(Long jobId);

    /**
     * 查询项目的导出任务列表
     * 
     * @param projectId 项目ID
     * @param status 任务状态（可选）
     * @return 任务列表
     */
    List<AnnotationExportJobs> getProjectExportTasks(Long projectId, ExportTaskStatus status);

    /**
     * 清理过期的导出文件和任务记录
     * 
     * @param retentionDays 保留天数
     * @return 清理的任务数量
     */
    int cleanupExpiredExportTasks(int retentionDays);

    /**
     * 获取导出进度
     * 
     * @param jobId 任务ID
     * @return 进度百分比 (0-100)
     */
    int getExportProgress(Long jobId);

    /**
     * 重新执行失败的导出任务
     * 
     * @param jobId 任务ID
     * @return 是否成功重新启动
     */
    boolean retryExportTask(Long jobId);

    /**
     * 批量导出多个项目
     * 
     * @param projectIds 项目ID列表
     * @param incrementalExport 是否增量导出
     * @return 创建的任务ID列表
     */
    List<Long> batchCreateExportTasks(List<Long> projectIds, boolean incrementalExport);

    /**
     * 获取支持的导出格式列表
     * 
     * @return 格式列表
     */
    List<String> getSupportedExportFormats();

    /**
     * 预估导出任务的时间和文件大小
     * 
     * @param projectId 项目ID
     * @param categoryIds 分类ID列表
     * @return 预估信息
     */
    ExportEstimation estimateExportTask(Long projectId, List<Long> categoryIds);

    /**
     * 导出预估信息
     */
    class ExportEstimation {
        private long estimatedDurationMinutes;
        private long estimatedFileSizeMB;
        private int totalImages;
        private int totalAnnotations;
        
        // Constructors, getters and setters
        public ExportEstimation() {}
        
        public ExportEstimation(long estimatedDurationMinutes, long estimatedFileSizeMB, 
                               int totalImages, int totalAnnotations) {
            this.estimatedDurationMinutes = estimatedDurationMinutes;
            this.estimatedFileSizeMB = estimatedFileSizeMB;
            this.totalImages = totalImages;
            this.totalAnnotations = totalAnnotations;
        }
        
        public long getEstimatedDurationMinutes() { return estimatedDurationMinutes; }
        public void setEstimatedDurationMinutes(long estimatedDurationMinutes) { this.estimatedDurationMinutes = estimatedDurationMinutes; }
        public long getEstimatedFileSizeMB() { return estimatedFileSizeMB; }
        public void setEstimatedFileSizeMB(long estimatedFileSizeMB) { this.estimatedFileSizeMB = estimatedFileSizeMB; }
        public int getTotalImages() { return totalImages; }
        public void setTotalImages(int totalImages) { this.totalImages = totalImages; }
        public int getTotalAnnotations() { return totalAnnotations; }
        public void setTotalAnnotations(int totalAnnotations) { this.totalAnnotations = totalAnnotations; }
    }
}

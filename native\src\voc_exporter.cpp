#include "voc_exporter.h"
#include <iostream>
#include <fstream>
#include <filesystem>

ExportResult VOCExporter::exportDataset(const ExportConfig& config, 
                                        const std::vector<ImageInfo>& images, 
                                        const std::string& categories) {
    ExportResult result;
    result.success = false;
    result.processedCount = 0;
    result.outputPath = config.outputPath;
    
    try {
        // 创建VOC目录结构
        std::filesystem::create_directories(config.outputPath + "/JPEGImages");
        std::filesystem::create_directories(config.outputPath + "/Annotations");
        std::filesystem::create_directories(config.outputPath + "/ImageSets/Main");
        
        // 处理每个图像
        for (const auto& image : images) {
            // 处理图像文件
            processImage(image, config, config.outputPath + "/JPEGImages");
            
            // 创建XML标注文件
            createAnnotationXML(image, config.outputPath + "/Annotations", categories);
            
            result.processedCount++;
        }
        
        // 创建数据集分割文件
        createDatasetSplitFiles(images, config.outputPath + "/ImageSets/Main");
        
        result.success = true;
        result.message = "VOC格式导出成功";
        
    } catch (const std::exception& e) {
        result.success = false;
        result.message = std::string("VOC导出失败: ") + e.what();
    }
    
    return result;
}

bool VOCExporter::createAnnotationXML(const ImageInfo& image,
                                     const std::string& outputPath,
                                     const std::string& categories) {
    try {
        std::string filename = std::filesystem::path(image.imagePath).stem().string() + ".xml";
        std::string fullPath = outputPath + "/" + filename;
        
        std::ofstream file(fullPath);
        if (!file.is_open()) {
            return false;
        }
        
        // 创建VOC XML格式
        file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        file << "<annotation>\n";
        file << "  <folder>JPEGImages</folder>\n";
        file << "  <filename>" << std::filesystem::path(image.imagePath).filename().string() << "</filename>\n";
        file << "  <path>" << image.imagePath << "</path>\n";
        file << "  <source>\n";
        file << "    <database>Unknown</database>\n";
        file << "  </source>\n";
        file << "  <size>\n";
        file << "    <width>" << image.originalWidth << "</width>\n";
        file << "    <height>" << image.originalHeight << "</height>\n";
        file << "    <depth>3</depth>\n";
        file << "  </size>\n";
        file << "  <segmented>0</segmented>\n";
        
        // 解析并添加标注对象
        std::string vocAnnotations = parseAnnotationToVOC(image.annotationData, image);
        file << vocAnnotations;
        
        file << "</annotation>\n";
        file.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "创建VOC XML文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool VOCExporter::processImage(const ImageInfo& image, 
                              const ExportConfig& config,
                              const std::string& outputDir) {
    try {
        // 这里应该调用ImageProcessor来处理图像变换
        // 暂时使用简单的文件复制
        std::string outputPath = outputDir + "/" + std::filesystem::path(image.imagePath).filename().string();
        std::filesystem::copy_file(image.imagePath, outputPath, std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "处理图像失败: " << e.what() << std::endl;
        return false;
    }
}

bool VOCExporter::createDatasetSplitFiles(const std::vector<ImageInfo>& images,
                                         const std::string& outputPath) {
    try {
        std::ofstream trainFile(outputPath + "/train.txt");
        std::ofstream valFile(outputPath + "/val.txt");
        std::ofstream testFile(outputPath + "/test.txt");
        
        for (const auto& image : images) {
            std::string basename = std::filesystem::path(image.imagePath).stem().string();
            
            switch (image.datasetType) {
                case DatasetType::TRAIN:
                    if (trainFile.is_open()) trainFile << basename << "\n";
                    break;
                case DatasetType::VALIDATION:
                    if (valFile.is_open()) valFile << basename << "\n";
                    break;
                case DatasetType::TEST:
                    if (testFile.is_open()) testFile << basename << "\n";
                    break;
            }
        }
        
        trainFile.close();
        valFile.close();
        testFile.close();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "创建数据集分割文件失败: " << e.what() << std::endl;
        return false;
    }
}

std::string VOCExporter::parseAnnotationToVOC(const std::string& annotationData,
                                              const ImageInfo& image) {
    // 这里需要解析JSON格式的标注数据并转换为VOC XML格式
    // 暂时返回空的对象标注
    return "  <!-- 标注对象将在这里添加 -->\n";
}

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a data annotation platform built with Spring Boot 3.x, Java 21, and PostgreSQL. The platform provides tools for image annotation, dataset management, and export functionality in various formats (COCO, VOC, YOLO).

Key features include:
- Image annotation tools with rectangle and polygon support
- Dataset management and organization
- Export to multiple formats (COCO, VOC, YOLO)
- Image processing capabilities (cropping, transformation)
- High-performance C++ native library for image processing via JNI

## Repository Structure

```
data-annotation-platform/
├── ylzx-admin/           # Main application entry point
├── ylzx-annotation/      # Core annotation functionality
├── ylzx-common/          # Shared utilities and constants
├── ylzx-framework/       # Framework-level components (security, caching)
├── ylzx-system/          # System management (users, roles, permissions)
├── ylzx-flowable/        # Workflow engine integration
├── ylzx-generator/       # Code generation tools
├── ylzx-quartz/          # Scheduled tasks
├── native/               # C++ native library for high-performance operations
├── annotation_data/      # Data storage directory for annotations
└── sql/                  # Database initialization scripts
```

## Common Development Commands

### Building the Project
```bash
# Build the entire project
mvn clean install

# Build without running tests
mvn clean install -DskipTests
```

### Running the Application
```bash
# Run from command line
mvn spring-boot:run -pl ylzx-admin

# Or run the main class directly
# YlzxApplication.java in ylzx-admin module
```

### Running Tests
```bash
# Run all tests
mvn test

# Run tests for a specific module
mvn test -pl ylzx-annotation

# Run a specific test class
mvn test -Dtest=ImageCropServiceTest -pl ylzx-annotation
```

### Database Migration
```bash
# Initialize database with scripts in sql/ directory
# Default database: PostgreSQL
# Database name: data-annotation-platform
```

## Architecture Overview

### Backend Stack
- **Framework**: Spring Boot 3.x with Jakarta EE
- **Language**: Java 21
- **Database**: PostgreSQL
- **ORM**: MyBatis-Plus
- **Security**: Spring Security 6.x
- **Caching**: Redis
- **Build Tool**: Maven
- **API Documentation**: SpringDoc OpenAPI

### Key Modules
1. **ylzx-annotation**: Core annotation functionality including image processing, dataset export, and annotation management
2. **ylzx-admin**: Main application entry point and configuration
3. **ylzx-framework**: Security, caching, and cross-cutting concerns
4. **ylzx-system**: User management, roles, permissions, and system configuration

### Native Library Integration
The platform includes a high-performance C++ native library for image processing operations:
- Located in the `native/` directory
- Integrated via JNI through `DatasetExportNative.java`
- Provides optimized image transformation and dataset export capabilities
- Supports multiple export formats (COCO, VOC, YOLO)

### Data Flow
1. Images are uploaded and stored in the `annotation_data/` directory
2. Annotations are created and managed through the web interface
3. Datasets can be exported in various formats using the native library
4. Image processing operations (cropping, transformation) are handled by the C++ library for performance

## Configuration Files
- `application.yml`: Main configuration file
- `application-druid.yml`: Database connection pool configuration
- `application-prod.yml`: Production environment configuration

## Important Directories
- `annotation_data/`: Root directory for all annotation-related files
- `native/`: C++ native library source code
- `sql/`: Database initialization scripts
- `uploadPath/`: Default upload directory for user files
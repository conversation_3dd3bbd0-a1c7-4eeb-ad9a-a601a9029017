<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>ylzx</artifactId>
		<groupId>com.ylzx</groupId>
		<version>3.9.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>

	<artifactId>ylzx-flowable</artifactId>

	<dependencies>

		<dependency>
			<groupId>com.ylzx</groupId>
			<artifactId>ylzx-framework</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ylzx</groupId>
			<artifactId>ylzx-system</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ylzx</groupId>
			<artifactId>ylzx-common</artifactId>
		</dependency>

		<!--常用工具类 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<!-- JSON工具类 -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>

		<!-- 阿里JSON解析器 -->
		<dependency>
			<groupId>com.alibaba.fastjson2</groupId>
			<artifactId>fastjson2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.flowable</groupId>
			<artifactId>flowable-spring-boot-starter</artifactId>
			<!-- 排除flowable自带的权限认证 -->
			<exclusions>
				<exclusion>
					<groupId>org.flowable</groupId>
					<artifactId>flowable-spring-security</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.flowable</groupId>
			<artifactId>flowable-engine</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>

		<dependency>
			<!-- websocket -->
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

		<!--el表达式计算-->
		<dependency>
			<groupId>com.googlecode.aviator</groupId>
			<artifactId>aviator</artifactId>
			<version>5.3.3</version>
		</dependency>

	</dependencies>

<!--	<build>-->
<!--		<plugins>-->
<!--			<plugin>-->
<!--				<groupId>org.springframework.boot</groupId>-->
<!--				<artifactId>spring-boot-maven-plugin</artifactId>-->
<!--			</plugin>-->
<!--		</plugins>-->
<!--	</build>-->

</project>

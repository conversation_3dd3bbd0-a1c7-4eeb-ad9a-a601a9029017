package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ylzx.annotation.mapper.AnnotationProjectsMapper;
import com.ylzx.annotation.domain.AnnotationProjects;
import com.ylzx.annotation.service.AnnotationProjectsService;
import com.ylzx.annotation.service.AnnotationProjectCategoriesService;
import lombok.extern.slf4j.Slf4j;

/**
 * 标注项目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Slf4j
@Service
public class AnnotationProjectsServiceImpl extends ServiceImpl<AnnotationProjectsMapper,AnnotationProjects> implements AnnotationProjectsService
{
    @Autowired
    private AnnotationProjectsMapper annotationProjectsMapper;

    @Autowired
    private AnnotationProjectCategoriesService annotationProjectCategoriesService;

    /**
     * 查询标注项目
     * 
     * @param projectId 标注项目主键
     * @return 标注项目
     */
    @Override
    public AnnotationProjects selectAnnotationProjectsByProjectId(Long projectId)
    {
        return annotationProjectsMapper.selectAnnotationProjectsByProjectId(projectId);
    }

    /**
     * 查询标注项目列表
     * 
     * @param annotationProjects 标注项目
     * @return 标注项目集合
     */
    @Override
    public List<AnnotationProjects> selectAnnotationProjectsList(AnnotationProjects annotationProjects)
    {
        return annotationProjectsMapper.selectAnnotationProjectsList(annotationProjects);
    }

    /**
     * 新增标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    @Override
    public int insertAnnotationProjects(AnnotationProjects annotationProjects)
    {
        return annotationProjectsMapper.insertAnnotationProjects(annotationProjects);
    }

    /**
     * 修改标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    @Override
    public int updateAnnotationProjects(AnnotationProjects annotationProjects)
    {
        return annotationProjectsMapper.updateAnnotationProjects(annotationProjects);
    }

    /**
     * 批量删除标注项目
     * 
     * @param projectIds 需要删除的标注项目主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectsByProjectIds(Long[] projectIds)
    {
        return annotationProjectsMapper.deleteAnnotationProjectsByProjectIds(projectIds);
    }

    /**
     * 删除标注项目信息
     *
     * @param projectId 标注项目主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectsByProjectId(Long projectId)
    {
        return annotationProjectsMapper.deleteAnnotationProjectsByProjectId(projectId);
    }

    /**
     * 根据项目ID查询项目配置（包含关联的分类信息）
     *
     * @param projectId 项目ID
     * @return 项目配置
     */
    @Override
    public AnnotationProjects selectProjectWithCategoriesByProjectId(Long projectId)
    {
        // 查询项目信息
        AnnotationProjects project = annotationProjectsMapper.selectAnnotationProjectsByProjectId(projectId);

        if (project != null) {
            // 查询关联的分类ID列表
            List<Long> categoryIds = annotationProjectCategoriesService.selectCategoryIdsByProjectId(projectId);
            log.debug("项目ID为{}的配置查询成功，关联分类数量: {}", projectId, categoryIds.size());
        }

        return project;
    }

    /**
     * 保存项目及其分类关联关系
     *
     * @param project 项目信息
     * @param categoryIds 分类ID列表
     * @return 是否保存成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProjectWithCategories(AnnotationProjects project, List<Long> categoryIds)
    {
        try {
            // 保存项目信息
            int projectResult = insertAnnotationProjects(project);
            if (projectResult <= 0) {
                log.error("保存项目信息失败");
                return false;
            }

            // 保存分类关联关系
            if (categoryIds != null && !categoryIds.isEmpty()) {
                Long projectId = project.getProjectId();
                boolean categoryResult = annotationProjectCategoriesService.updateProjectCategories(projectId, categoryIds);
                if (!categoryResult) {
                    log.error("保存项目分类关联关系失败");
                    throw new RuntimeException("保存项目分类关联关系失败");
                }
            }

            log.info("项目及分类关联关系保存成功，项目ID: {}", project.getProjectId());
            return true;
        } catch (Exception e) {
            log.error("保存项目及分类关联关系失败", e);
            throw e;
        }
    }

    /**
     * 更新项目及其分类关联关系
     *
     * @param project 项目信息
     * @param categoryIds 分类ID列表
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProjectWithCategories(AnnotationProjects project, List<Long> categoryIds)
    {
        try {
            // 更新项目信息
            int projectResult = updateAnnotationProjects(project);
            if (projectResult <= 0) {
                log.error("更新项目信息失败");
                return false;
            }

            // 更新分类关联关系
            if (categoryIds != null) {
                Long projectId = project.getProjectId();
                boolean categoryResult = annotationProjectCategoriesService.updateProjectCategories(projectId, categoryIds);
                if (!categoryResult) {
                    log.error("更新项目分类关联关系失败");
                    throw new RuntimeException("更新项目分类关联关系失败");
                }
            }

            log.info("项目及分类关联关系更新成功，项目ID: {}", project.getProjectId());
            return true;
        } catch (Exception e) {
            log.error("更新项目及分类关联关系失败", e);
            throw e;
        }
    }
}

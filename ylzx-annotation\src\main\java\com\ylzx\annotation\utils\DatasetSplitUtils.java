package com.ylzx.annotation.utils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据集分割工具类
 * 使用一致性哈希算法确保数据集分割的稳定性和可重复性
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
public class DatasetSplitUtils {

    /**
     * 数据集类型枚举
     */
    public enum DatasetType {
        TRAIN("train"),
        VALIDATION("validation"), 
        TEST("test");
        
        private final String value;
        
        DatasetType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }

    /**
     * 数据集分割结果
     */
    public static class SplitResult {
        private final DatasetType type;
        private final String typeName;
        
        public SplitResult(DatasetType type) {
            this.type = type;
            this.typeName = type.getValue();
        }
        
        public DatasetType getType() {
            return type;
        }
        
        public String getTypeName() {
            return typeName;
        }
    }

    /**
     * 根据图片ID和分割比例确定数据集类型
     * 使用一致性哈希算法，确保相同的图片ID总是分配到相同的数据集
     * 
     * @param imageId 图片ID
     * @param trainRatio 训练集比例 (0-1)
     * @param validationRatio 验证集比例 (0-1)
     * @param testRatio 测试集比例 (0-1)
     * @return 数据集分割结果
     */
    public static SplitResult splitDataset(Long imageId, BigDecimal trainRatio, 
                                         BigDecimal validationRatio, BigDecimal testRatio) {
        // 验证比例参数
        validateRatios(trainRatio, validationRatio, testRatio);
        
        // 使用图片ID生成一致性哈希值
        double hashValue = generateConsistentHash(imageId);
        
        // 根据哈希值和比例确定数据集类型
        double trainThreshold = trainRatio.doubleValue();
        double validationThreshold = trainThreshold + validationRatio.doubleValue();
        
        if (hashValue < trainThreshold) {
            return new SplitResult(DatasetType.TRAIN);
        } else if (hashValue < validationThreshold) {
            return new SplitResult(DatasetType.VALIDATION);
        } else {
            return new SplitResult(DatasetType.TEST);
        }
    }

    /**
     * 批量分割数据集
     * 
     * @param imageIds 图片ID列表
     * @param trainRatio 训练集比例
     * @param validationRatio 验证集比例
     * @param testRatio 测试集比例
     * @return 分割结果列表，与输入的imageIds顺序对应
     */
    public static List<SplitResult> batchSplitDataset(List<Long> imageIds, BigDecimal trainRatio,
                                                    BigDecimal validationRatio, BigDecimal testRatio) {
        validateRatios(trainRatio, validationRatio, testRatio);
        
        List<SplitResult> results = new ArrayList<>(imageIds.size());
        for (Long imageId : imageIds) {
            results.add(splitDataset(imageId, trainRatio, validationRatio, testRatio));
        }
        
        return results;
    }

    /**
     * 生成一致性哈希值 (0-1之间)
     * 使用MD5哈希算法确保分布均匀性
     * 
     * @param imageId 图片ID
     * @return 0-1之间的哈希值
     */
    private static double generateConsistentHash(Long imageId) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(imageId.toString().getBytes(StandardCharsets.UTF_8));
            
            // 取前4个字节转换为int，然后归一化到0-1
            int hashInt = 0;
            for (int i = 0; i < 4; i++) {
                hashInt = (hashInt << 8) | (hashBytes[i] & 0xFF);
            }
            
            // 转换为0-1之间的值
            return Math.abs(hashInt) / (double) Integer.MAX_VALUE;
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5算法不可用，使用简单哈希", e);
            // 降级方案：使用简单的模运算
            return (imageId % 10000) / 10000.0;
        }
    }

    /**
     * 验证分割比例的有效性
     * 
     * @param trainRatio 训练集比例
     * @param validationRatio 验证集比例
     * @param testRatio 测试集比例
     */
    private static void validateRatios(BigDecimal trainRatio, BigDecimal validationRatio, BigDecimal testRatio) {
        if (trainRatio == null || validationRatio == null || testRatio == null) {
            throw new IllegalArgumentException("分割比例不能为空");
        }
        
        if (trainRatio.compareTo(BigDecimal.ZERO) < 0 || trainRatio.compareTo(BigDecimal.ONE) > 0 ||
            validationRatio.compareTo(BigDecimal.ZERO) < 0 || validationRatio.compareTo(BigDecimal.ONE) > 0 ||
            testRatio.compareTo(BigDecimal.ZERO) < 0 || testRatio.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalArgumentException("分割比例必须在0-1之间");
        }
        
        BigDecimal sum = trainRatio.add(validationRatio).add(testRatio);
        if (sum.compareTo(BigDecimal.ONE) != 0) {
            throw new IllegalArgumentException("分割比例之和必须等于1，当前和为: " + sum);
        }
    }

    /**
     * 获取默认的数据集分割比例
     * 训练集70%，验证集20%，测试集10%
     * 
     * @return 包含三个比例的数组 [训练集, 验证集, 测试集]
     */
    public static BigDecimal[] getDefaultRatios() {
        return new BigDecimal[]{
            new BigDecimal("0.7"),  // 训练集
            new BigDecimal("0.2"),  // 验证集
            new BigDecimal("0.1")   // 测试集
        };
    }
}

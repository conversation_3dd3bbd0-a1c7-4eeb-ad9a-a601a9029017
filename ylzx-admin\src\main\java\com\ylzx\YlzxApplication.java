package com.ylzx;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StreamUtils;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@EnableScheduling
public class YlzxApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(YlzxApplication.class, args);
        System.out.println("                          云岭咨询平台启动成功\n");
        try {
            ClassPathResource resource = new ClassPathResource("boot_banner.txt");
            String banner = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            System.out.println(banner);
        } catch (IOException e) {
            System.out.println("未找到启动横幅文件: boot_banner.txt");
        }
    }
}

#include "common.h"
#include <algorithm>
#include <cctype>
#include <stdexcept>

std::string formatToString(ExportFormat format) {
    switch (format) {
        case ExportFormat::COCO: return "coco";
        case ExportFormat::VOC: return "voc";
        case ExportFormat::YOLO: return "yolo";
        default: return "unknown";
    }
}

ExportFormat stringToFormat(const std::string& format) {
    std::string lower = format;
    std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
    
    if (lower == "coco") return ExportFormat::COCO;
    if (lower == "voc") return ExportFormat::VOC;
    if (lower == "yolo") return ExportFormat::YOLO;
    
    throw std::invalid_argument("Unknown export format: " + format);
}

std::string datasetTypeToString(DatasetType type) {
    switch (type) {
        case DatasetType::TRAIN: return "train";
        case DatasetType::VALIDATION: return "validation";
        case DatasetType::TEST: return "test";
        default: return "unknown";
    }
}

DatasetType stringToDatasetType(const std::string& type) {
    std::string lower = type;
    std::transform(lower.begin(), lower.end(), lower.begin(), ::tolower);
    
    if (lower == "train") return DatasetType::TRAIN;
    if (lower == "validation") return DatasetType::VALIDATION;
    if (lower == "test") return DatasetType::TEST;
    
    throw std::invalid_argument("Unknown dataset type: " + type);
}

package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationProjectsService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationProjects;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 标注项目Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注项目管理")
@RestController
@RequestMapping("/annotation/projects")
public class AnnotationProjectsController extends BaseController
{
    @Resource
    private AnnotationProjectsService annotationProjectsService;

    /**
     * 查询标注项目列表
     */
    @ApiOperation("查询标注项目列表")
    @PreAuthorize("@ss.hasPermi('system:projects:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnotationProjects annotationProjects)
    {
        startPage();
        List<AnnotationProjects> list = annotationProjectsService.selectAnnotationProjectsList(annotationProjects);
        return getDataTable(list);
    }

    /**
     * 导出标注项目列表
     */
    @PreAuthorize("@ss.hasPermi('system:projects:export')")
    @Log(title = "标注项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationProjects annotationProjects)
    {
        List<AnnotationProjects> list = annotationProjectsService.selectAnnotationProjectsList(annotationProjects);
        ExcelUtil<AnnotationProjects> util = new ExcelUtil<AnnotationProjects>(AnnotationProjects.class);
        util.exportExcel(response, list, "标注项目数据");
    }

    /**
     * 获取标注项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:projects:query')")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId)
    {
        return success(annotationProjectsService.selectAnnotationProjectsByProjectId(projectId));
    }

    /**
     * 新增标注项目
     */
    @PreAuthorize("@ss.hasPermi('system:projects:add')")
    @Log(title = "标注项目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationProjects annotationProjects)
    {
        return toAjax(annotationProjectsService.insertAnnotationProjects(annotationProjects));
    }

    /**
     * 修改标注项目
     */
    @PreAuthorize("@ss.hasPermi('system:projects:edit')")
    @Log(title = "标注项目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationProjects annotationProjects)
    {
        return toAjax(annotationProjectsService.updateAnnotationProjects(annotationProjects));
    }

    /**
     * 删除标注项目
     */
    @PreAuthorize("@ss.hasPermi('system:projects:remove')")
    @Log(title = "标注项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable("projectIds") Long[] projectIds)
    {
        return toAjax(annotationProjectsService.deleteAnnotationProjectsByProjectIds(projectIds));
    }
}

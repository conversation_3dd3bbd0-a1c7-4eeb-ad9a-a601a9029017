# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\code\java\data-annotation-platform\native

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\code\java\data-annotation-platform\native\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/dataset_export.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/dataset_export.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/dataset_export.dir/clean
clean: CMakeFiles/generate_jni_headers.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/dataset_export.dir

# All Build rule for target.
CMakeFiles/dataset_export.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\code\java\data-annotation-platform\native\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8 "Built target dataset_export"
.PHONY : CMakeFiles/dataset_export.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/dataset_export.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\code\java\data-annotation-platform\native\build\CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/dataset_export.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\code\java\data-annotation-platform\native\build\CMakeFiles 0
.PHONY : CMakeFiles/dataset_export.dir/rule

# Convenience name for target.
dataset_export: CMakeFiles/dataset_export.dir/rule
.PHONY : dataset_export

# codegen rule for target.
CMakeFiles/dataset_export.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\code\java\data-annotation-platform\native\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8 "Finished codegen for target dataset_export"
.PHONY : CMakeFiles/dataset_export.dir/codegen

# clean rule for target.
CMakeFiles/dataset_export.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\dataset_export.dir\build.make CMakeFiles/dataset_export.dir/clean
.PHONY : CMakeFiles/dataset_export.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/generate_jni_headers.dir

# All Build rule for target.
CMakeFiles/generate_jni_headers.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\generate_jni_headers.dir\build.make CMakeFiles/generate_jni_headers.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\generate_jni_headers.dir\build.make CMakeFiles/generate_jni_headers.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\code\java\data-annotation-platform\native\build\CMakeFiles --progress-num=9 "Built target generate_jni_headers"
.PHONY : CMakeFiles/generate_jni_headers.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/generate_jni_headers.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\code\java\data-annotation-platform\native\build\CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/generate_jni_headers.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\work\code\java\data-annotation-platform\native\build\CMakeFiles 0
.PHONY : CMakeFiles/generate_jni_headers.dir/rule

# Convenience name for target.
generate_jni_headers: CMakeFiles/generate_jni_headers.dir/rule
.PHONY : generate_jni_headers

# codegen rule for target.
CMakeFiles/generate_jni_headers.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\generate_jni_headers.dir\build.make CMakeFiles/generate_jni_headers.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\work\code\java\data-annotation-platform\native\build\CMakeFiles --progress-num=9 "Finished codegen for target generate_jni_headers"
.PHONY : CMakeFiles/generate_jni_headers.dir/codegen

# clean rule for target.
CMakeFiles/generate_jni_headers.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\generate_jni_headers.dir\build.make CMakeFiles/generate_jni_headers.dir/clean
.PHONY : CMakeFiles/generate_jni_headers.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system


package com.ylzx.annotation.controller;

import java.util.List;
import com.ylzx.annotation.service.AnnotationImageSourcesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.io.IOException;
import java.util.Map;
import java.util.HashMap;
import com.ylzx.annotation.config.AnnotationPathProperties;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 标注图片来源Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注图片来源管理")
@RestController
@RequestMapping("/annotation/sources")
public class AnnotationImageSourcesController extends BaseController
{
    @Resource
    private AnnotationImageSourcesService annotationImageSourcesService;

    @Autowired
    private AnnotationPathProperties pathProperties;

    /**
     * 查询标注图片来源列表
     */
    @ApiOperation("查询标注图片来源列表")
    @PreAuthorize("@ss.hasPermi('system:sources:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") AnnotationImageSources annotationImageSources)
    {
        startPage();
        List<AnnotationImageSources> list = annotationImageSourcesService.selectAnnotationImageSourcesList(annotationImageSources);
        return getDataTable(list);
    }

    /**
     * 导出标注图片来源列表
     */
    @PreAuthorize("@ss.hasPermi('system:sources:export')")
    @Log(title = "标注图片来源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationImageSources annotationImageSources)
    {
        List<AnnotationImageSources> list = annotationImageSourcesService.selectAnnotationImageSourcesList(annotationImageSources);
        ExcelUtil<AnnotationImageSources> util = new ExcelUtil<AnnotationImageSources>(AnnotationImageSources.class);
        util.exportExcel(response, list, "标注图片来源数据");
    }

    /**
     * 获取标注图片来源详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sources:query')")
    @GetMapping(value = "/{sourceId}")
    public AjaxResult getInfo(@PathVariable("sourceId") Long sourceId)
    {
        return success(annotationImageSourcesService.selectAnnotationImageSourcesBySourceId(sourceId));
    }

    /**
     * 新增标注图片来源
     */
    @PreAuthorize("@ss.hasPermi('system:sources:add')")
    @Log(title = "标注图片来源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationImageSources annotationImageSources)
    {
        return toAjax(annotationImageSourcesService.save(annotationImageSources));
    }

    /**
     * 修改标注图片来源
     */
    @PreAuthorize("@ss.hasPermi('system:sources:edit')")
    @Log(title = "标注图片来源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationImageSources annotationImageSources)
    {
        return toAjax(annotationImageSourcesService.updateById(annotationImageSources));
    }

    /**
     * 删除标注图片来源
     */
    @PreAuthorize("@ss.hasPermi('system:sources:remove')")
    @Log(title = "标注图片来源", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sourceIds}")
    public AjaxResult remove(@PathVariable("sourceIds") Long[] sourceIds)
    {
        return toAjax(annotationImageSourcesService.deleteAnnotationImageSourcesBySourceIds(sourceIds));
    }

    /**
     * 上传压缩文件来创建新的图片源
     *
     * @param file 上传的压缩文件 (zip, tar.gz)
     * @return 操作结果
     */
    @ApiOperation("上传压缩文件创建图片源")
    @PreAuthorize("@ss.hasPermi('annotation:sources:upload')")
    @PostMapping("/upload")
    public AjaxResult uploadSource(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }
        try {
            AnnotationImageSources source = annotationImageSourcesService.uploadSource(file);
            return AjaxResult.success("文件上传并处理成功", source);
        } catch (IllegalStateException e) {
            return AjaxResult.error(e.getMessage());
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("处理上传文件时发生未知错误", e);
            return AjaxResult.error("处理失败，发生未知错误");
        }
    }

    /**
     * 上传压缩文件并关联到指定分类
     *
     * @param file 上传的压缩文件 (zip, tar.gz)
     * @param categoryId 分类ID，如果指定则自动处理图片并关联到该分类
     * @return 操作结果
     */
    @ApiOperation("上传压缩文件并关联到指定分类")
    @PreAuthorize("@ss.hasPermi('annotation:sources:upload')")
    @PostMapping("/uploadWithCategory")
    public AjaxResult uploadSourceWithCategory(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "categoryId", required = false) Long categoryId) {
        if (file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }
        try {
            AnnotationImageSources source = annotationImageSourcesService.uploadSourceWithCategory(file, categoryId);
            if (categoryId != null) {
                return AjaxResult.success("文件上传、处理并关联到分类成功", source);
            } else {
                return AjaxResult.success("文件上传并处理成功", source);
            }
        } catch (IllegalStateException e) {
            return AjaxResult.error(e.getMessage());
        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("处理上传文件时发生未知错误", e);
            return AjaxResult.error("处理失败，发生未知错误");
        }
    }

    /**
     * 扫描并导入指定目录下的压缩文件
     *
     * @return 操作结果
     */
    @ApiOperation("扫描并导入源文件")
    @PreAuthorize("@ss.hasPermi('annotation:sources:scan')")
    @PostMapping("/scanImport")
    public AjaxResult scanAndImportSources() {
        try {
            annotationImageSourcesService.scanAndImportSources();
            return AjaxResult.success("扫描和导入任务已完成");
        } catch (IOException e) {
            logger.error("扫描导入过程中发生IO错误", e);
            return AjaxResult.error("扫描导入失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("扫描导入过程中发生未知错误", e);
            return AjaxResult.error("扫描导入失败，发生未知错误");
        }
    }

    /**
     * 手动触发扫描并导入，支持指定默认分类
     *
     * @param categoryId 默认分类ID，可选
     * @return 操作结果
     */
    @ApiOperation("手动触发扫描并导入，支持指定默认分类")
    @PreAuthorize("@ss.hasPermi('annotation:sources:scan')")
    @PostMapping("/triggerScan")
    public AjaxResult triggerScanAndImport(@RequestParam(required = false) Long categoryId) {
        try {
            if (categoryId != null) {
                annotationImageSourcesService.scanAndImportSourcesWithImageProcessing(categoryId);
                return AjaxResult.success("扫描、导入和图片处理任务已完成");
            } else {
                annotationImageSourcesService.scanAndImportSources();
                return AjaxResult.success("扫描和导入任务已完成");
            }
        } catch (IOException e) {
            logger.error("手动触发扫描过程中发生IO错误", e);
            return AjaxResult.error("扫描失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("手动触发扫描过程中发生未知错误", e);
            return AjaxResult.error("扫描失败，发生未知错误");
        }
    }

    /**
     * 扫描并导入指定目录下的压缩文件，同时自动处理图片
     *
     * @param categoryId 分类ID，如果指定则自动处理图片并关联到该分类
     * @return 操作结果
     */
    @ApiOperation("扫描并导入源文件，同时自动处理图片")
    @PreAuthorize("@ss.hasPermi('annotation:sources:scan')")
    @PostMapping("/scanImportWithImages")
    public AjaxResult scanAndImportSourcesWithImageProcessing(@RequestParam(required = false) Long categoryId) {
        try {
            annotationImageSourcesService.scanAndImportSourcesWithImageProcessing(categoryId);
            return AjaxResult.success("扫描、导入和图片处理任务已启动");
        } catch (IOException e) {
            logger.error("扫描导入和图片处理过程中发生IO错误", e);
            return AjaxResult.error("扫描导入和图片处理失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("扫描导入和图片处理过程中发生未知错误", e);
            return AjaxResult.error("扫描导入和图片处理失败，发生未知错误");
        }
    }

    /**
     * 获取扫描目录状态统计
     *
     * @return 目录状态统计信息
     */
    @ApiOperation("获取扫描目录状态统计")
    @PreAuthorize("@ss.hasPermi('annotation:sources:list')")
    @GetMapping("/scanStatus")
    public AjaxResult getScanDirectoryStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 统计各目录中的文件数量
            status.put("scanDirCount", countFilesInDirectory(pathProperties.getScanPath()));
            status.put("processedDirCount", countFilesInDirectory(pathProperties.getProcessedPath()));
            status.put("errorDirCount", countFilesInDirectory(pathProperties.getErrorPath()));

            // 统计数据库中的记录数量
            status.put("totalSources", annotationImageSourcesService.count());

            return AjaxResult.success(status);
        } catch (Exception e) {
            logger.error("获取扫描目录状态时发生错误", e);
            return AjaxResult.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取扫描目录中的压缩文件列表
     *
     * @return 压缩文件列表
     */
    @ApiOperation("获取扫描目录中的压缩文件列表")
    @PreAuthorize("@ss.hasPermi('annotation:sources:list')")
    @GetMapping("/scanFiles")
    public AjaxResult getScanDirectoryFiles() {
        try {
            List<Map<String, Object>> files = annotationImageSourcesService.getScanDirectoryFiles();
            return AjaxResult.success(files);
        } catch (Exception e) {
            logger.error("获取扫描目录文件列表时发生错误", e);
            return AjaxResult.error("获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 手动处理指定的扫描文件
     *
     * @param filename 文件名
     * @param categoryId 分类ID，可选
     * @return 处理结果
     */
    @ApiOperation("手动处理指定的扫描文件")
    @PreAuthorize("@ss.hasPermi('annotation:sources:scan')")
    @PostMapping("/processScanFile")
    public AjaxResult processScanFile(
            @RequestParam String filename,
            @RequestParam(required = false) Long categoryId) {
        try {
            AnnotationImageSources source = annotationImageSourcesService.processScanFile(filename, categoryId);
            if (categoryId != null) {
                return AjaxResult.success("文件处理成功并关联到分类", source);
            } else {
                return AjaxResult.success("文件处理成功，未关联分类", source);
            }
        } catch (IllegalStateException e) {
            return AjaxResult.error("文件内容重复: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("参数错误: " + e.getMessage());
        } catch (IOException e) {
            logger.error("处理扫描文件失败", e);
            return AjaxResult.error("处理文件失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("处理扫描文件时发生未知错误", e);
            return AjaxResult.error("处理失败，发生未知错误");
        }
    }

    /**
     * 统计目录中的文件数量
     */
    private int countFilesInDirectory(java.nio.file.Path directory) {
        try (java.nio.file.DirectoryStream<java.nio.file.Path> stream =
             java.nio.file.Files.newDirectoryStream(directory)) {
            int count = 0;
            for (java.nio.file.Path path : stream) {
                if (java.nio.file.Files.isRegularFile(path)) {
                    count++;
                }
            }
            return count;
        } catch (Exception e) {
            logger.warn("统计目录文件数量失败: {}", directory, e);
            return 0;
        }
    }
}

#ifndef IMAGE_PROCESSOR_H
#define IMAGE_PROCESSOR_H

#include <string>

/**
 * 图像处理器
 */
class ImageProcessor {
public:
    /**
     * 应用图像变换
     * 
     * @param imagePath 输入图像路径
     * @param outputPath 输出图像路径
     * @param transformations 变换配置JSON
     * @return 是否成功
     */
    bool applyTransformations(const std::string& imagePath,
                             const std::string& outputPath,
                             const std::string& transformations);

private:
    /**
     * 调整图像尺寸
     */
    bool resizeImage(const std::string& imagePath,
                    const std::string& outputPath,
                    int targetWidth, int targetHeight);
    
    /**
     * 旋转图像
     */
    bool rotateImage(const std::string& imagePath,
                    const std::string& outputPath,
                    double angle);
    
    /**
     * 转换为灰度图像
     */
    bool convertToGrayscale(const std::string& imagePath,
                           const std::string& outputPath);
    
    /**
     * 应用缩放
     */
    bool scaleImage(const std::string& imagePath,
                   const std::string& outputPath,
                   double scaleRatio);
    
    /**
     * 应用mask
     */
    bool applyMask(const std::string& imagePath,
                  const std::string& outputPath,
                  const std::string& maskConfig);
    
    /**
     * 解析变换配置JSON
     */
    bool parseTransformations(const std::string& transformations);
};

#endif // IMAGE_PROCESSOR_H

package com.ylzx.annotation.config;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import jakarta.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "ylzx.annotation")
public class AnnotationPathProperties {

    /**
     * 文件操作的根目录
     */
    private String basePath;
    
    /**
     * 上传文件目录名称
     */
    private String uploadDir = "upload";
    
    /**
     * 解压文件目录名称
     */
    private String extractionDir = "extraction";
    
    /**
     * 扫描文件目录名称
     */
    private String scanDir = "scan";
    
    /**
     * 处理完成的文件目录名称
     */
    private String processedDir = "processed";

    /**
     * 处理失败的文件目录名称
     */
    private String errorDir = "error";

    /**
     * 是否在定时扫描时自动处理图片
     */
    private boolean autoProcessImages = false;

    /**
     * 自动处理图片时使用的默认分类ID
     */
    private Long defaultCategoryId;

    private Path uploadPath;
    private Path extractionBasePath;
    private Path scanPath;
    private Path processedPath;
    private Path errorPath;

    @PostConstruct
    public void init() {
        log.info("初始化标注文件路径，根目录为: {}", basePath);
        try {
            if (basePath == null || basePath.isEmpty()) {
                throw new IllegalStateException("ylzx.annotation.base-path 不能为空");
            }
            Path root = Paths.get(basePath);
            this.uploadPath = root.resolve(uploadDir);
            this.extractionBasePath = root.resolve(extractionDir);
            this.scanPath = root.resolve(scanDir);
            this.processedPath = root.resolve(processedDir);
            this.errorPath = root.resolve(errorDir);

            Files.createDirectories(uploadPath);
            Files.createDirectories(extractionBasePath);
            Files.createDirectories(scanPath);
            Files.createDirectories(processedPath);
            Files.createDirectories(errorPath);
            log.info("成功创建所有标注文件子目录");
        } catch (IOException e) {
            log.error("创建标注文件目录失败", e);
            throw new RuntimeException("创建标注文件目录失败", e);
        }
    }
} 
package com.ylzx.annotation.domain;

import java.io.Serial;

import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注图片对象 annotation_images
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationImages extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注图片主键 */
    private Long imageId;

    /** 标注分类主键 */
    @Excel(name = "标注分类主键")
    private Long categoryId;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String originalFilename;

    /** 存储路径 */
    @Excel(name = "存储路径")
    private String storagePath;

    /** MD5哈希 */
    @Excel(name = "MD5哈希")
    private String md5Hash;

    /** 宽度 */
    @Excel(name = "宽度")
    private Long width;

    /** 高度 */
    @Excel(name = "高度")
    private Long height;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long fileSizeBytes;

    /** 图片来源主键 */
    @Excel(name = "图片来源主键")
    private Long sourceId;

    /** 上传时间 */
    @Excel(name = "上传时间")
    private String uploadedAt;

    /** 标注状态 */
    @Excel(name = "标注状态", readConverterExp = "0=未标注,1=未审核,2=审核通过,3=审核不通过")
    private AnnotationStatus status;
}

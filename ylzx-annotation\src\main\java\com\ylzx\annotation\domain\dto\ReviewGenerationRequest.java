package com.ylzx.annotation.domain.dto;

import java.time.LocalDateTime;
import java.math.BigDecimal;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;

/**
 * 抽审记录生成请求DTO
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "抽审记录生成请求")
public class ReviewGenerationRequest {

    @Schema(description = "标注分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "开始时间", example = "2025-01-01T00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2025-01-24T23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "抽审比例(0-1之间的小数，如0.1表示10%)", example = "0.1")
    @DecimalMin(value = "0.0", inclusive = false, message = "抽审比例必须大于0")
    @DecimalMax(value = "1.0", inclusive = true, message = "抽审比例不能超过1")
    private BigDecimal reviewRatio;

    @Schema(description = "抽审数量", example = "10")
    @Min(value = 1, message = "抽审数量必须大于0")
    private Integer reviewCount;

    /**
     * 验证参数有效性
     */
    public boolean isValid() {
        // 抽审比例和抽审数量至少指定一个
        return reviewRatio != null || reviewCount != null;
    }
}

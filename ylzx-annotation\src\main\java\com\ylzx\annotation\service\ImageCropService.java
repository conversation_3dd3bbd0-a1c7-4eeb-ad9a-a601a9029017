package com.ylzx.annotation.service;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;

import java.util.List;

/**
 * 图像裁剪服务接口
 * 根据标注坐标智能裁剪图像
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ImageCropService {

    /**
     * 根据标注坐标裁剪图像
     * 
     * @param image 图像信息
     * @param annotations 标注列表
     * @param outputPath 输出路径
     * @param cropConfig 裁剪配置
     * @return 裁剪结果
     */
    CropResult cropImageByAnnotations(AnnotationImages image, List<AnnotationAnnotations> annotations, 
                                    String outputPath, CropConfig cropConfig);

    /**
     * 批量裁剪图像
     * 
     * @param imageAnnotationPairs 图像和标注对列表
     * @param outputBasePath 输出基础路径
     * @param cropConfig 裁剪配置
     * @return 批量裁剪结果
     */
    BatchCropResult batchCropImages(List<ImageAnnotationPair> imageAnnotationPairs, 
                                  String outputBasePath, CropConfig cropConfig);

    /**
     * 图像和标注对
     */
    class ImageAnnotationPair {
        private AnnotationImages image;
        private List<AnnotationAnnotations> annotations;

        public ImageAnnotationPair(AnnotationImages image, List<AnnotationAnnotations> annotations) {
            this.image = image;
            this.annotations = annotations;
        }

        // Getters and setters
        public AnnotationImages getImage() { return image; }
        public void setImage(AnnotationImages image) { this.image = image; }
        public List<AnnotationAnnotations> getAnnotations() { return annotations; }
        public void setAnnotations(List<AnnotationAnnotations> annotations) { this.annotations = annotations; }
    }

    /**
     * 裁剪配置
     */
    class CropConfig {
        /** 目标裁剪尺寸（如果为null则根据标注自动计算） */
        private Integer targetWidth;
        private Integer targetHeight;
        
        /** 边距（像素） */
        private int padding = 20;
        
        /** 是否启用随机位置放置 */
        private boolean enableRandomPlacement = true;
        
        /** 最小裁剪尺寸 */
        private int minCropWidth = 100;
        private int minCropHeight = 100;
        
        /** 最大裁剪尺寸 */
        private int maxCropWidth = 1024;
        private int maxCropHeight = 1024;
        
        /** 是否保持宽高比 */
        private boolean maintainAspectRatio = true;
        
        /** 背景填充颜色（RGB） */
        private int[] backgroundColor = {128, 128, 128}; // 灰色背景

        // Constructors
        public CropConfig() {}

        public CropConfig(Integer targetWidth, Integer targetHeight) {
            this.targetWidth = targetWidth;
            this.targetHeight = targetHeight;
        }

        // Getters and setters
        public Integer getTargetWidth() { return targetWidth; }
        public void setTargetWidth(Integer targetWidth) { this.targetWidth = targetWidth; }
        public Integer getTargetHeight() { return targetHeight; }
        public void setTargetHeight(Integer targetHeight) { this.targetHeight = targetHeight; }
        public int getPadding() { return padding; }
        public void setPadding(int padding) { this.padding = padding; }
        public boolean isEnableRandomPlacement() { return enableRandomPlacement; }
        public void setEnableRandomPlacement(boolean enableRandomPlacement) { this.enableRandomPlacement = enableRandomPlacement; }
        public int getMinCropWidth() { return minCropWidth; }
        public void setMinCropWidth(int minCropWidth) { this.minCropWidth = minCropWidth; }
        public int getMinCropHeight() { return minCropHeight; }
        public void setMinCropHeight(int minCropHeight) { this.minCropHeight = minCropHeight; }
        public int getMaxCropWidth() { return maxCropWidth; }
        public void setMaxCropWidth(int maxCropWidth) { this.maxCropWidth = maxCropWidth; }
        public int getMaxCropHeight() { return maxCropHeight; }
        public void setMaxCropHeight(int maxCropHeight) { this.maxCropHeight = maxCropHeight; }
        public boolean isMaintainAspectRatio() { return maintainAspectRatio; }
        public void setMaintainAspectRatio(boolean maintainAspectRatio) { this.maintainAspectRatio = maintainAspectRatio; }
        public int[] getBackgroundColor() { return backgroundColor; }
        public void setBackgroundColor(int[] backgroundColor) { this.backgroundColor = backgroundColor; }
    }

    /**
     * 裁剪结果
     */
    class CropResult {
        private boolean success;
        private String message;
        private String outputPath;
        private BoundingBox originalBounds;  // 原始标注边界框
        private BoundingBox cropBounds;      // 实际裁剪区域
        private int cropWidth;
        private int cropHeight;
        private long processingTimeMs;

        // Constructors
        public CropResult() {}

        public CropResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public CropResult(boolean success, String message, String outputPath) {
            this.success = success;
            this.message = message;
            this.outputPath = outputPath;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getOutputPath() { return outputPath; }
        public void setOutputPath(String outputPath) { this.outputPath = outputPath; }
        public BoundingBox getOriginalBounds() { return originalBounds; }
        public void setOriginalBounds(BoundingBox originalBounds) { this.originalBounds = originalBounds; }
        public BoundingBox getCropBounds() { return cropBounds; }
        public void setCropBounds(BoundingBox cropBounds) { this.cropBounds = cropBounds; }
        public int getCropWidth() { return cropWidth; }
        public void setCropWidth(int cropWidth) { this.cropWidth = cropWidth; }
        public int getCropHeight() { return cropHeight; }
        public void setCropHeight(int cropHeight) { this.cropHeight = cropHeight; }
        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
    }

    /**
     * 批量裁剪结果
     */
    class BatchCropResult {
        private boolean success;
        private String message;
        private int totalCount;
        private int successCount;
        private int failedCount;
        private List<CropResult> results;
        private long totalProcessingTimeMs;

        // Constructors
        public BatchCropResult() {}

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        public List<CropResult> getResults() { return results; }
        public void setResults(List<CropResult> results) { this.results = results; }
        public long getTotalProcessingTimeMs() { return totalProcessingTimeMs; }
        public void setTotalProcessingTimeMs(long totalProcessingTimeMs) { this.totalProcessingTimeMs = totalProcessingTimeMs; }
    }

    /**
     * 边界框
     */
    class BoundingBox {
        private int x;
        private int y;
        private int width;
        private int height;

        public BoundingBox(int x, int y, int width, int height) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }

        // Getters and setters
        public int getX() { return x; }
        public void setX(int x) { this.x = x; }
        public int getY() { return y; }
        public void setY(int y) { this.y = y; }
        public int getWidth() { return width; }
        public void setWidth(int width) { this.width = width; }
        public int getHeight() { return height; }
        public void setHeight(int height) { this.height = height; }

        public int getRight() { return x + width; }
        public int getBottom() { return y + height; }

        @Override
        public String toString() {
            return String.format("BoundingBox{x=%d, y=%d, width=%d, height=%d}", x, y, width, height);
        }
    }
}

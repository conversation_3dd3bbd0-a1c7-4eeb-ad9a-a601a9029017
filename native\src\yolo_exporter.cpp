#include "yolo_exporter.h"
#include <iostream>
#include <fstream>
#include <filesystem>

ExportResult YOLOExporter::exportDataset(const ExportConfig& config, 
                                         const std::vector<ImageInfo>& images, 
                                         const std::string& categories) {
    ExportResult result;
    result.success = false;
    result.processedCount = 0;
    result.outputPath = config.outputPath;
    
    try {
        // 创建YOLO目录结构
        std::filesystem::create_directories(config.outputPath + "/images/train");
        std::filesystem::create_directories(config.outputPath + "/images/val");
        std::filesystem::create_directories(config.outputPath + "/images/test");
        std::filesystem::create_directories(config.outputPath + "/labels/train");
        std::filesystem::create_directories(config.outputPath + "/labels/val");
        std::filesystem::create_directories(config.outputPath + "/labels/test");
        
        // 处理每个图像
        for (const auto& image : images) {
            std::string imageSubdir, labelSubdir;
            
            switch (image.datasetType) {
                case DatasetType::TRAIN:
                    imageSubdir = "/images/train";
                    labelSubdir = "/labels/train";
                    break;
                case DatasetType::VALIDATION:
                    imageSubdir = "/images/val";
                    labelSubdir = "/labels/val";
                    break;
                case DatasetType::TEST:
                    imageSubdir = "/images/test";
                    labelSubdir = "/labels/test";
                    break;
            }
            
            // 处理图像文件
            processImage(image, config, config.outputPath + imageSubdir);
            
            // 创建标注文件
            createAnnotationTxt(image, config.outputPath + labelSubdir, categories);
            
            result.processedCount++;
        }
        
        // 创建data.yaml配置文件
        createDataYaml(images, config.outputPath, categories);
        
        result.success = true;
        result.message = "YOLO格式导出成功";
        
    } catch (const std::exception& e) {
        result.success = false;
        result.message = std::string("YOLO导出失败: ") + e.what();
    }
    
    return result;
}

bool YOLOExporter::createAnnotationTxt(const ImageInfo& image,
                                      const std::string& outputPath,
                                      const std::string& categories) {
    try {
        std::string filename = std::filesystem::path(image.imagePath).stem().string() + ".txt";
        std::string fullPath = outputPath + "/" + filename;
        
        std::ofstream file(fullPath);
        if (!file.is_open()) {
            return false;
        }
        
        // 解析并转换标注数据为YOLO格式
        std::string yoloAnnotations = parseAnnotationToYOLO(image.annotationData, image, categories);
        file << yoloAnnotations;
        
        file.close();
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "创建YOLO标注文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool YOLOExporter::processImage(const ImageInfo& image, 
                               const ExportConfig& config,
                               const std::string& outputDir) {
    try {
        // 这里应该调用ImageProcessor来处理图像变换
        // 暂时使用简单的文件复制
        std::string outputPath = outputDir + "/" + std::filesystem::path(image.imagePath).filename().string();
        std::filesystem::copy_file(image.imagePath, outputPath, std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "处理图像失败: " << e.what() << std::endl;
        return false;
    }
}

bool YOLOExporter::createDataYaml(const std::vector<ImageInfo>& images,
                                 const std::string& outputPath,
                                 const std::string& categories) {
    try {
        std::ofstream file(outputPath + "/data.yaml");
        if (!file.is_open()) {
            return false;
        }
        
        // 统计各数据集的图像数量
        int trainCount = 0, valCount = 0, testCount = 0;
        for (const auto& image : images) {
            switch (image.datasetType) {
                case DatasetType::TRAIN: trainCount++; break;
                case DatasetType::VALIDATION: valCount++; break;
                case DatasetType::TEST: testCount++; break;
            }
        }
        
        // 写入YAML配置
        file << "# Dataset configuration for YOLO\n";
        file << "path: " << outputPath << "\n";
        if (trainCount > 0) file << "train: images/train\n";
        if (valCount > 0) file << "val: images/val\n";
        if (testCount > 0) file << "test: images/test\n";
        file << "\n";
        
        // 这里需要解析categories JSON来获取类别信息
        file << "# Classes\n";
        file << "nc: 1  # number of classes\n";
        file << "names: ['object']  # class names\n";
        
        file.close();
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "创建data.yaml文件失败: " << e.what() << std::endl;
        return false;
    }
}

std::string YOLOExporter::parseAnnotationToYOLO(const std::string& annotationData,
                                               const ImageInfo& image,
                                               const std::string& categories) {
    // 这里需要解析JSON格式的标注数据并转换为YOLO格式
    // YOLO格式: class_id center_x center_y width height (相对坐标 0-1)
    // 暂时返回空字符串
    return "";
}

std::string YOLOExporter::convertCoordinates(const std::string& coordinates,
                                            int imageWidth, int imageHeight) {
    // 这里需要实现坐标转换逻辑
    // 从绝对坐标转换为相对坐标
    return "";
}

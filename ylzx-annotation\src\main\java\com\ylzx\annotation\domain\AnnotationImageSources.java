package com.ylzx.annotation.domain;

import java.io.Serial;

import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注图片来源对象 annotation_image_sources
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationImageSources extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注图片来源主键 */
    private Long sourceId;

    /** 图片来源名称 */
    @Excel(name = "图片来源名称")
    private String sourceName;

    /** 上传类型 */
    @Excel(name = "上传类型")
    private String uploadType;

    /** 上传用户主键 */
    @Excel(name = "上传用户主键")
    private Long uploadedByUserId;

    /** 上传时间 */
    @Excel(name = "上传时间")
    private String uploadedAt;

    /** 元数据 */
    @Excel(name = "元数据")
    private String metadata;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String path;

    /** 压缩包路径 */
    @Excel(name = "压缩包路径")
    private String archivePath;

    /** 内容哈希 */
    @Excel(name = "内容哈希")
    private String contentHash;
}

package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationLabelsService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationLabels;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 标注标签Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注标签管理")
@RestController
@RequestMapping("/annotation/labels")
public class AnnotationLabelsController extends BaseController
{
    @Resource
    private AnnotationLabelsService annotationLabelsService;

    /**
     * 查询标注标签列表
     */
    @ApiOperation("查询标注标签列表")
    @PreAuthorize("@ss.hasPermi('system:labels:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnotationLabels annotationLabels)
    {
        startPage();
        List<AnnotationLabels> list = annotationLabelsService.selectAnnotationLabelsList(annotationLabels);
        return getDataTable(list);
    }

    /**
     * 导出标注标签列表
     */
    @PreAuthorize("@ss.hasPermi('system:labels:export')")
    @Log(title = "标注标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationLabels annotationLabels)
    {
        List<AnnotationLabels> list = annotationLabelsService.selectAnnotationLabelsList(annotationLabels);
        ExcelUtil<AnnotationLabels> util = new ExcelUtil<AnnotationLabels>(AnnotationLabels.class);
        util.exportExcel(response, list, "标注标签数据");
    }

    /**
     * 获取标注标签详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:labels:query')")
    @GetMapping(value = "/{labelId}")
    public AjaxResult getInfo(@PathVariable("labelId") Long labelId)
    {
        return success(annotationLabelsService.selectAnnotationLabelsByLabelId(labelId));
    }

    /**
     * 新增标注标签
     */
    @PreAuthorize("@ss.hasPermi('system:labels:add')")
    @Log(title = "标注标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationLabels annotationLabels)
    {
        return toAjax(annotationLabelsService.insertAnnotationLabels(annotationLabels));
    }

    /**
     * 修改标注标签
     */
    @PreAuthorize("@ss.hasPermi('system:labels:edit')")
    @Log(title = "标注标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationLabels annotationLabels)
    {
        return toAjax(annotationLabelsService.updateAnnotationLabels(annotationLabels));
    }

    /**
     * 删除标注标签
     */
    @PreAuthorize("@ss.hasPermi('system:labels:remove')")
    @Log(title = "标注标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{labelIds}")
    public AjaxResult remove(@PathVariable("labelIds") Long[] labelIds)
    {
        return toAjax(annotationLabelsService.deleteAnnotationLabelsByLabelIds(labelIds));
    }
}

package com.ylzx.common.core.utils.annotation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 表示2D坐标系中的一个点
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Point {

    private double x;
    private double y;

    /**
     * 从 "x y" 格式的字符串解析点.
     *
     * @param pointString a String in "x y" format (e.g., "100.5 200.0")
     * @return a new Point object
     * @throws IllegalArgumentException if the string format is invalid
     */
    public static Point fromString(String pointString) {
        if (pointString == null || pointString.trim().isEmpty()) {
            throw new IllegalArgumentException("点坐标字符串不能为空。");
        }
        String[] parts = pointString.trim().split("\\s+");
        if (parts.length != 2) {
            throw new IllegalArgumentException("无效的点坐标格式。预期格式为 'x y'，但接收到: " + pointString);
        }
        try {
            double x = Double.parseDouble(parts[0]);
            double y = Double.parseDouble(parts[1]);
            return new Point(x, y);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的数字格式: " + pointString, e);
        }
    }

    /**
     * 将点格式化为 "x y" 字符串.
     *
     * @return a String in "x y" format
     */
    @Override
    public String toString() {
        return x + " " + y;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Point point = (Point) o;
        return Double.compare(point.x, x) == 0 &&
                Double.compare(point.y, y) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(x, y);
    }
} 
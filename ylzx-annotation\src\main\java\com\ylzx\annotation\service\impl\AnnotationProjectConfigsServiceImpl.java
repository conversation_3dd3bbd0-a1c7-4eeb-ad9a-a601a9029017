package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ylzx.annotation.mapper.AnnotationProjectConfigsMapper;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;
import com.ylzx.annotation.service.AnnotationProjectConfigsService;
import com.ylzx.annotation.service.AnnotationProjectCategoriesService;
import lombok.extern.slf4j.Slf4j;

/**
 * 标注项目配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Slf4j
@Service
public class AnnotationProjectConfigsServiceImpl extends ServiceImpl<AnnotationProjectConfigsMapper,AnnotationProjectConfigs> implements AnnotationProjectConfigsService
{
    @Autowired
    private AnnotationProjectConfigsMapper annotationProjectConfigsMapper;

    @Autowired
    private AnnotationProjectCategoriesService annotationProjectCategoriesService;

    /**
     * 查询标注项目配置
     * 
     * @param configId 标注项目配置主键
     * @return 标注项目配置
     */
    @Override
    public AnnotationProjectConfigs selectAnnotationProjectConfigsByConfigId(Long configId)
    {
        return annotationProjectConfigsMapper.selectAnnotationProjectConfigsByConfigId(configId);
    }

    /**
     * 查询标注项目配置列表
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 标注项目配置集合
     */
    @Override
    public List<AnnotationProjectConfigs> selectAnnotationProjectConfigsList(AnnotationProjectConfigs annotationProjectConfigs)
    {
        return annotationProjectConfigsMapper.selectAnnotationProjectConfigsList(annotationProjectConfigs);
    }

    /**
     * 新增标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    @Override
    public int insertAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs)
    {
        return annotationProjectConfigsMapper.insertAnnotationProjectConfigs(annotationProjectConfigs);
    }

    /**
     * 修改标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    @Override
    public int updateAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs)
    {
        return annotationProjectConfigsMapper.updateAnnotationProjectConfigs(annotationProjectConfigs);
    }

    /**
     * 批量删除标注项目配置
     * 
     * @param configIds 需要删除的标注项目配置主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectConfigsByConfigIds(Long[] configIds)
    {
        return annotationProjectConfigsMapper.deleteAnnotationProjectConfigsByConfigIds(configIds);
    }

    /**
     * 删除标注项目配置信息
     * 
     * @param configId 标注项目配置主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationProjectConfigsByConfigId(Long configId)
    {
        return annotationProjectConfigsMapper.deleteAnnotationProjectConfigsByConfigId(configId);
    }

    /**
     * 根据项目ID查询项目配置（包含关联的分类信息）
     *
     * @param projectId 项目ID
     * @return 项目配置
     */
    @Override
    public AnnotationProjectConfigs selectConfigWithCategoriesByProjectId(Long projectId)
    {
        // 查询项目配置
        AnnotationProjectConfigs config = annotationProjectConfigsMapper.selectAnnotationProjectConfigsList(
            AnnotationProjectConfigs.builder().projectId(projectId.toString()).build()
        ).stream().findFirst().orElse(null);

        if (config != null) {
            // 查询关联的分类ID列表
            List<Long> categoryIds = annotationProjectCategoriesService.selectCategoryIdsByProjectId(projectId);
            // 这里可以将categoryIds设置到config的某个字段中，或者创建一个包装类
            log.debug("项目ID为{}的配置查询成功，关联分类数量: {}", projectId, categoryIds.size());
        }

        return config;
    }

    /**
     * 保存项目配置及其分类关联关系
     *
     * @param config 项目配置
     * @param categoryIds 分类ID列表
     * @return 是否保存成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveConfigWithCategories(AnnotationProjectConfigs config, List<Long> categoryIds)
    {
        try {
            // 保存项目配置
            int configResult = insertAnnotationProjectConfigs(config);
            if (configResult <= 0) {
                log.error("保存项目配置失败");
                return false;
            }

            // 保存分类关联关系
            if (categoryIds != null && !categoryIds.isEmpty()) {
                Long projectId = Long.valueOf(config.getProjectId());
                boolean categoryResult = annotationProjectCategoriesService.updateProjectCategories(projectId, categoryIds);
                if (!categoryResult) {
                    log.error("保存项目分类关联关系失败");
                    throw new RuntimeException("保存项目分类关联关系失败");
                }
            }

            log.info("保存项目配置及分类关联关系成功，项目ID: {}, 分类数量: {}",
                    config.getProjectId(), categoryIds != null ? categoryIds.size() : 0);
            return true;

        } catch (Exception e) {
            log.error("保存项目配置及分类关联关系失败，项目ID: {}", config.getProjectId(), e);
            throw e;
        }
    }

    /**
     * 更新项目配置及其分类关联关系
     *
     * @param config 项目配置
     * @param categoryIds 分类ID列表
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfigWithCategories(AnnotationProjectConfigs config, List<Long> categoryIds)
    {
        try {
            // 更新项目配置
            int configResult = updateAnnotationProjectConfigs(config);
            if (configResult <= 0) {
                log.error("更新项目配置失败");
                return false;
            }

            // 更新分类关联关系
            if (categoryIds != null) {
                Long projectId = Long.valueOf(config.getProjectId());
                boolean categoryResult = annotationProjectCategoriesService.updateProjectCategories(projectId, categoryIds);
                if (!categoryResult) {
                    log.error("更新项目分类关联关系失败");
                    throw new RuntimeException("更新项目分类关联关系失败");
                }
            }

            log.info("更新项目配置及分类关联关系成功，项目ID: {}, 分类数量: {}",
                    config.getProjectId(), categoryIds != null ? categoryIds.size() : 0);
            return true;

        } catch (Exception e) {
            log.error("更新项目配置及分类关联关系失败，项目ID: {}", config.getProjectId(), e);
            throw e;
        }
    }
}

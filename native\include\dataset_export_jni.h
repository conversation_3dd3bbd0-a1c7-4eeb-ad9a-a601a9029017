#ifndef DATASET_EXPORT_JNI_H
#define DATASET_EXPORT_JNI_H

#include <jni.h>
#include "common.h"

#ifdef __cplusplus
extern "C" {
#endif

// J<PERSON>方法声明
JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportCOCO
  (JNIEnv *, jobject, jobject, jobjectArray, jstring);

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportVOC
  (JNIEnv *, jobject, jobject, jobjectArray, jstring);

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportYOLO
  (JNIEnv *, jobject, jobject, jobjectArray, jstring);

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_applyImageTransformations
  (JNIEnv *, jobject, jstring, jstring, jstring);

JNIEXPORT jstring JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_validateConfig
  (JNIEnv *, jobject, jobject);

JNIEXPORT jobjectArray JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_getSupportedFormats
  (JNIEnv *, jobject);

JNIEXPORT jstring JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_getLibraryVersion
  (JNIEnv *, jobject);

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_cleanupTempFiles
  (JNIEnv *, jobject, jstring);

#ifdef __cplusplus
}
#endif

// C++辅助函数
ExportConfig javaConfigToNative(JNIEnv* env, jobject jconfig);
std::vector<ImageInfo> javaImagesToNative(JNIEnv* env, jobjectArray jimages);
jobject nativeResultToJava(JNIEnv* env, const ExportResult& result);

#endif // DATASET_EXPORT_JNI_H

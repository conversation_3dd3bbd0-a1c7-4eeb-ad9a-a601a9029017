package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注项目配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationProjectConfigsService extends IService<AnnotationProjectConfigs>
{
    /**
     * 查询标注项目配置
     * 
     * @param configId 标注项目配置主键
     * @return 标注项目配置
     */
    AnnotationProjectConfigs selectAnnotationProjectConfigsByConfigId(Long configId);

    /**
     * 查询标注项目配置列表
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 标注项目配置集合
     */
    List<AnnotationProjectConfigs> selectAnnotationProjectConfigsList(AnnotationProjectConfigs annotationProjectConfigs);

    /**
     * 新增标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    int insertAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs);

    /**
     * 修改标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    int updateAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs);

    /**
     * 批量删除标注项目配置
     * 
     * @param configIds 需要删除的标注项目配置主键集合
     * @return 结果
     */
    int deleteAnnotationProjectConfigsByConfigIds(Long[] configIds);

    /**
     * 删除标注项目配置信息
     *
     * @param configId 标注项目配置主键
     * @return 结果
     */
    int deleteAnnotationProjectConfigsByConfigId(Long configId);

    /**
     * 根据项目ID查询项目配置（包含关联的分类信息）
     *
     * @param projectId 项目ID
     * @return 项目配置
     */
    AnnotationProjectConfigs selectConfigWithCategoriesByProjectId(Long projectId);

    /**
     * 保存项目配置及其分类关联关系
     *
     * @param config 项目配置
     * @param categoryIds 分类ID列表
     * @return 是否保存成功
     */
    boolean saveConfigWithCategories(AnnotationProjectConfigs config, java.util.List<Long> categoryIds);

    /**
     * 更新项目配置及其分类关联关系
     *
     * @param config 项目配置
     * @param categoryIds 分类ID列表
     * @return 是否更新成功
     */
    boolean updateConfigWithCategories(AnnotationProjectConfigs config, java.util.List<Long> categoryIds);
}

package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标注项目配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationProjectConfigsMapper extends BaseMapper<AnnotationProjectConfigs>
{
    /**
     * 查询标注项目配置
     * 
     * @param configId 标注项目配置主键
     * @return 标注项目配置
     */
    AnnotationProjectConfigs selectAnnotationProjectConfigsByConfigId(Long configId);

    /**
     * 查询标注项目配置列表
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 标注项目配置集合
     */
    List<AnnotationProjectConfigs> selectAnnotationProjectConfigsList(AnnotationProjectConfigs annotationProjectConfigs);

    /**
     * 新增标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    int insertAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs);

    /**
     * 修改标注项目配置
     * 
     * @param annotationProjectConfigs 标注项目配置
     * @return 结果
     */
    int updateAnnotationProjectConfigs(AnnotationProjectConfigs annotationProjectConfigs);

    /**
     * 删除标注项目配置
     * 
     * @param configId 标注项目配置主键
     * @return 结果
     */
    int deleteAnnotationProjectConfigsByConfigId(Long configId);

    /**
     * 批量删除标注项目配置
     * 
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationProjectConfigsByConfigIds(Long[] configIds);
}

package com.ylzx.annotation.controller;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.service.AnnotationAnnotationsService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.service.AnnotationCategoriesImagesService;
import com.ylzx.annotation.service.ImageCropService;
import com.ylzx.annotation.service.ImageCropService.*;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 图像裁剪控制器
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Tag(name = "图像裁剪管理", description = "图像裁剪相关接口")
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/annotation/crop")
public class ImageCropController extends BaseController {

    private final ImageCropService imageCropService;
    private final AnnotationImagesService annotationImagesService;
    private final AnnotationAnnotationsService annotationAnnotationsService;
    private final AnnotationCategoriesImagesService annotationCategoriesImagesService;

    @Operation(summary = "单张图像裁剪", description = "根据标注坐标裁剪单张图像")
    @PostMapping("/single")
    public AjaxResult cropSingleImage(@Valid @RequestBody CropSingleImageRequest request) {
        try {
            // 获取图像信息
            AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(request.getImageId());
            if (image == null) {
                return error("图像不存在");
            }

            // 获取标注信息
            AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
            queryAnnotation.setImageId(request.getImageId());
            List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);
            if (annotations.isEmpty()) {
                return error("该图像没有标注数据");
            }

            // 构建裁剪配置
            CropConfig config = buildCropConfig(request.getCropConfig());

            // 生成输出路径
            String outputPath = generateOutputPath(image, request.getOutputDir());

            // 执行裁剪
            CropResult result = imageCropService.cropImageByAnnotations(image, annotations, outputPath, config);

            if (result.isSuccess()) {
                return AjaxResult.success("图像裁剪成功", result);
            } else {
                return error("图像裁剪失败: " + result.getMessage());
            }

        } catch (Exception e) {
            log.error("单张图像裁剪失败", e);
            return error("图像裁剪失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量图像裁剪", description = "批量裁剪多张图像")
    @PostMapping("/batch")
    public AjaxResult cropBatchImages(@Valid @RequestBody CropBatchImagesRequest request) {
        try {
            // 准备图像和标注对
            List<ImageAnnotationPair> pairs = new ArrayList<>();
            
            for (Long imageId : request.getImageIds()) {
                AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(imageId);
                if (image == null) {
                    log.warn("图像不存在: {}", imageId);
                    continue;
                }

                AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
                queryAnnotation.setImageId(imageId);
                List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);
                if (annotations.isEmpty()) {
                    log.warn("图像没有标注数据: {}", imageId);
                    continue;
                }

                pairs.add(new ImageAnnotationPair(image, annotations));
            }

            if (pairs.isEmpty()) {
                return error("没有有效的图像数据");
            }

            // 构建裁剪配置
            CropConfig config = buildCropConfig(request.getCropConfig());

            // 执行批量裁剪
            BatchCropResult result = imageCropService.batchCropImages(pairs, request.getOutputDir(), config);

            return AjaxResult.success("批量裁剪完成", result);

        } catch (Exception e) {
            log.error("批量图像裁剪失败", e);
            return error("批量图像裁剪失败: " + e.getMessage());
        }
    }

    @Operation(summary = "按分类批量裁剪", description = "按分类ID批量裁剪图像")
    @PostMapping("/batch/category")
    public AjaxResult cropImagesByCategory(@Valid @RequestBody CropByCategoryRequest request) {
        try {
            // 获取分类下的所有图像任务
            AnnotationCategoriesImages queryTask = new AnnotationCategoriesImages();
            queryTask.setCategoryId(request.getCategoryId());
            List<AnnotationCategoriesImages> tasks = annotationCategoriesImagesService.selectAnnotationCategoriesImagesList(queryTask);
            if (tasks.isEmpty()) {
                return error("该分类下没有图像任务");
            }

            // 准备图像和标注对
            List<ImageAnnotationPair> pairs = new ArrayList<>();

            for (AnnotationCategoriesImages task : tasks) {
                AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(task.getImageId());
                if (image == null) {
                    continue;
                }

                AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
                queryAnnotation.setImageId(image.getImageId());
                List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);
                if (!annotations.isEmpty()) {
                    pairs.add(new ImageAnnotationPair(image, annotations));
                }
            }

            if (pairs.isEmpty()) {
                return error("该分类下没有有效的标注数据");
            }

            // 构建裁剪配置
            CropConfig config = buildCropConfig(request.getCropConfig());

            // 生成输出目录
            String outputDir = Paths.get(request.getOutputDir(), "category_" + request.getCategoryId()).toString();

            // 执行批量裁剪
            BatchCropResult result = imageCropService.batchCropImages(pairs, outputDir, config);

            return AjaxResult.success("按分类批量裁剪完成", result);

        } catch (Exception e) {
            log.error("按分类批量裁剪失败", e);
            return error("按分类批量裁剪失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取裁剪预览", description = "获取图像裁剪的预览信息（不实际裁剪）")
    @GetMapping("/preview/{imageId}")
    public AjaxResult getCropPreview(
            @Parameter(description = "图像ID") @PathVariable Long imageId,
            @Parameter(description = "目标宽度") @RequestParam(required = false) Integer targetWidth,
            @Parameter(description = "目标高度") @RequestParam(required = false) Integer targetHeight,
            @Parameter(description = "边距") @RequestParam(defaultValue = "20") Integer padding) {
        
        try {
            // 获取图像信息
            AnnotationImages image = annotationImagesService.selectAnnotationImagesByImageId(imageId);
            if (image == null) {
                return error("图像不存在");
            }

            // 获取标注信息
            AnnotationAnnotations queryAnnotation = new AnnotationAnnotations();
            queryAnnotation.setImageId(imageId);
            List<AnnotationAnnotations> annotations = annotationAnnotationsService.selectAnnotationAnnotationsList(queryAnnotation);
            if (annotations.isEmpty()) {
                return error("该图像没有标注数据");
            }

            // 计算预览信息
            CropPreviewInfo previewInfo = calculateCropPreview(image, annotations, targetWidth, targetHeight, padding);

            return AjaxResult.success("获取裁剪预览成功", previewInfo);

        } catch (Exception e) {
            log.error("获取裁剪预览失败", e);
            return error("获取裁剪预览失败: " + e.getMessage());
        }
    }

    // 辅助方法

    private CropConfig buildCropConfig(CropConfigRequest request) {
        CropConfig config = new CropConfig();
        
        if (request != null) {
            config.setTargetWidth(request.getTargetWidth());
            config.setTargetHeight(request.getTargetHeight());
            config.setPadding(request.getPadding() != null ? request.getPadding() : 20);
            config.setEnableRandomPlacement(request.getEnableRandomPlacement() != null ? request.getEnableRandomPlacement() : true);
            config.setMaintainAspectRatio(request.getMaintainAspectRatio() != null ? request.getMaintainAspectRatio() : true);
            
            if (request.getBackgroundColor() != null && request.getBackgroundColor().length == 3) {
                config.setBackgroundColor(request.getBackgroundColor());
            }
        }
        
        return config;
    }

    private String generateOutputPath(AnnotationImages image, String outputDir) {
        String originalName = image.getOriginalFilename();
        String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
        String extension = originalName.substring(originalName.lastIndexOf('.'));
        
        String fileName = String.format("%s_crop_%d%s", baseName, image.getImageId(), extension);
        return Paths.get(outputDir, fileName).toString();
    }

    private CropPreviewInfo calculateCropPreview(AnnotationImages image, List<AnnotationAnnotations> annotations,
                                               Integer targetWidth, Integer targetHeight, Integer padding) {
        // 这里实现裁剪预览计算逻辑
        CropPreviewInfo previewInfo = new CropPreviewInfo();
        previewInfo.setImageId(image.getImageId());
        previewInfo.setOriginalWidth(image.getWidth().intValue());
        previewInfo.setOriginalHeight(image.getHeight().intValue());
        
        // 计算标注边界框
        // 这里可以调用 AnnotationGeometryUtils.calculateUnionBoundingBox
        
        return previewInfo;
    }

    // 请求和响应类

    public static class CropSingleImageRequest {
        @NotNull(message = "图像ID不能为空")
        private Long imageId;
        
        @NotNull(message = "输出目录不能为空")
        private String outputDir;
        
        private CropConfigRequest cropConfig;

        // Getters and setters
        public Long getImageId() { return imageId; }
        public void setImageId(Long imageId) { this.imageId = imageId; }
        public String getOutputDir() { return outputDir; }
        public void setOutputDir(String outputDir) { this.outputDir = outputDir; }
        public CropConfigRequest getCropConfig() { return cropConfig; }
        public void setCropConfig(CropConfigRequest cropConfig) { this.cropConfig = cropConfig; }
    }

    public static class CropBatchImagesRequest {
        @NotNull(message = "图像ID列表不能为空")
        private List<Long> imageIds;
        
        @NotNull(message = "输出目录不能为空")
        private String outputDir;
        
        private CropConfigRequest cropConfig;

        // Getters and setters
        public List<Long> getImageIds() { return imageIds; }
        public void setImageIds(List<Long> imageIds) { this.imageIds = imageIds; }
        public String getOutputDir() { return outputDir; }
        public void setOutputDir(String outputDir) { this.outputDir = outputDir; }
        public CropConfigRequest getCropConfig() { return cropConfig; }
        public void setCropConfig(CropConfigRequest cropConfig) { this.cropConfig = cropConfig; }
    }

    public static class CropByCategoryRequest {
        @NotNull(message = "分类ID不能为空")
        private Long categoryId;
        
        @NotNull(message = "输出目录不能为空")
        private String outputDir;
        
        private CropConfigRequest cropConfig;

        // Getters and setters
        public Long getCategoryId() { return categoryId; }
        public void setCategoryId(Long categoryId) { this.categoryId = categoryId; }
        public String getOutputDir() { return outputDir; }
        public void setOutputDir(String outputDir) { this.outputDir = outputDir; }
        public CropConfigRequest getCropConfig() { return cropConfig; }
        public void setCropConfig(CropConfigRequest cropConfig) { this.cropConfig = cropConfig; }
    }

    public static class CropConfigRequest {
        private Integer targetWidth;
        private Integer targetHeight;
        private Integer padding;
        private Boolean enableRandomPlacement;
        private Boolean maintainAspectRatio;
        private int[] backgroundColor;

        // Getters and setters
        public Integer getTargetWidth() { return targetWidth; }
        public void setTargetWidth(Integer targetWidth) { this.targetWidth = targetWidth; }
        public Integer getTargetHeight() { return targetHeight; }
        public void setTargetHeight(Integer targetHeight) { this.targetHeight = targetHeight; }
        public Integer getPadding() { return padding; }
        public void setPadding(Integer padding) { this.padding = padding; }
        public Boolean getEnableRandomPlacement() { return enableRandomPlacement; }
        public void setEnableRandomPlacement(Boolean enableRandomPlacement) { this.enableRandomPlacement = enableRandomPlacement; }
        public Boolean getMaintainAspectRatio() { return maintainAspectRatio; }
        public void setMaintainAspectRatio(Boolean maintainAspectRatio) { this.maintainAspectRatio = maintainAspectRatio; }
        public int[] getBackgroundColor() { return backgroundColor; }
        public void setBackgroundColor(int[] backgroundColor) { this.backgroundColor = backgroundColor; }
    }

    public static class CropPreviewInfo {
        private Long imageId;
        private int originalWidth;
        private int originalHeight;
        private BoundingBox annotationBounds;
        private BoundingBox cropBounds;
        private int estimatedCropWidth;
        private int estimatedCropHeight;

        // Getters and setters
        public Long getImageId() { return imageId; }
        public void setImageId(Long imageId) { this.imageId = imageId; }
        public int getOriginalWidth() { return originalWidth; }
        public void setOriginalWidth(int originalWidth) { this.originalWidth = originalWidth; }
        public int getOriginalHeight() { return originalHeight; }
        public void setOriginalHeight(int originalHeight) { this.originalHeight = originalHeight; }
        public BoundingBox getAnnotationBounds() { return annotationBounds; }
        public void setAnnotationBounds(BoundingBox annotationBounds) { this.annotationBounds = annotationBounds; }
        public BoundingBox getCropBounds() { return cropBounds; }
        public void setCropBounds(BoundingBox cropBounds) { this.cropBounds = cropBounds; }
        public int getEstimatedCropWidth() { return estimatedCropWidth; }
        public void setEstimatedCropWidth(int estimatedCropWidth) { this.estimatedCropWidth = estimatedCropWidth; }
        public int getEstimatedCropHeight() { return estimatedCropHeight; }
        public void setEstimatedCropHeight(int estimatedCropHeight) { this.estimatedCropHeight = estimatedCropHeight; }
    }
}

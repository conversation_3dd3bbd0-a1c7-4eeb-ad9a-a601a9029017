package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationImages;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注图片Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationImagesService extends IService<AnnotationImages>
{
    /**
     * 查询标注图片
     * 
     * @param imageId 标注图片主键
     * @return 标注图片
     */
    AnnotationImages selectAnnotationImagesByImageId(Long imageId);

    /**
     * 查询标注图片列表
     * 
     * @param annotationImages 标注图片
     * @return 标注图片集合
     */
    List<AnnotationImages> selectAnnotationImagesList(AnnotationImages annotationImages);

    /**
     * 新增标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */
    int insertAnnotationImages(AnnotationImages annotationImages);

    /**
     * 修改标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */
    int updateAnnotationImages(AnnotationImages annotationImages);

    /**
     * 批量删除标注图片
     * 
     * @param imageIds 需要删除的标注图片主键集合
     * @return 结果
     */
    int deleteAnnotationImagesByImageIds(Long[] imageIds);

    /**
     * 删除标注图片信息
     * 
     * @param imageId 标注图片主键
     * @return 结果
     */
    int deleteAnnotationImagesByImageId(Long imageId);

    /**
     * 根据数据源ID，处理压缩包并批量导入图片。
     *
     * @param sourceId 数据源ID
     * @param categoryId 标注分类ID
     */
    void processImagesFromSourceId(Long sourceId, Long categoryId);
}

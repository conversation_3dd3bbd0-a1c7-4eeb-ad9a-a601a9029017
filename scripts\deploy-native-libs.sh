#!/bin/bash
# 部署本地库文件到resources目录的脚本

echo "部署本地库文件..."

RESOURCES_DIR="ylzx-annotation/src/main/resources/native"
BUILD_DIR="native/build"

# 检查构建目录是否存在
if [ ! -d "$BUILD_DIR" ]; then
    echo "错误: 构建目录不存在: $BUILD_DIR"
    echo "请先编译本地库"
    exit 1
fi

# 检查资源目录是否存在
if [ ! -d "$RESOURCES_DIR" ]; then
    echo "创建资源目录: $RESOURCES_DIR"
    mkdir -p "$RESOURCES_DIR"
fi

# 创建架构特定目录
mkdir -p "$RESOURCES_DIR/amd64"
mkdir -p "$RESOURCES_DIR/aarch64"

# 检测当前平台
OS_NAME=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

# 标准化架构名称
case $ARCH in
    x86_64|amd64)
        ARCH_DIR="amd64"
        ;;
    aarch64|arm64)
        ARCH_DIR="aarch64"
        ;;
    *)
        echo "警告: 未知架构 $ARCH，使用通用目录"
        ARCH_DIR=""
        ;;
esac

# 复制库文件
if [ "$OS_NAME" = "linux" ]; then
    LIB_FILE="libdataset_export.so"
    if [ -f "$BUILD_DIR/lib/$LIB_FILE" ]; then
        echo "复制Linux库文件..."
        cp "$BUILD_DIR/lib/$LIB_FILE" "$RESOURCES_DIR/$LIB_FILE"
        if [ -n "$ARCH_DIR" ]; then
            cp "$BUILD_DIR/lib/$LIB_FILE" "$RESOURCES_DIR/$ARCH_DIR/$LIB_FILE"
        fi
        echo "Linux库文件复制完成"
    else
        echo "警告: 找不到Linux库文件: $BUILD_DIR/lib/$LIB_FILE"
    fi
elif [ "$OS_NAME" = "darwin" ]; then
    LIB_FILE="libdataset_export.dylib"
    if [ -f "$BUILD_DIR/lib/$LIB_FILE" ]; then
        echo "复制macOS库文件..."
        cp "$BUILD_DIR/lib/$LIB_FILE" "$RESOURCES_DIR/$LIB_FILE"
        if [ -n "$ARCH_DIR" ]; then
            cp "$BUILD_DIR/lib/$LIB_FILE" "$RESOURCES_DIR/$ARCH_DIR/$LIB_FILE"
        fi
        echo "macOS库文件复制完成"
    else
        echo "警告: 找不到macOS库文件: $BUILD_DIR/lib/$LIB_FILE"
    fi
else
    echo "警告: 未知操作系统 $OS_NAME"
fi

# 显示当前库文件
echo
echo "当前库文件:"
find "$RESOURCES_DIR" -name "*.so" -o -name "*.dylib" -o -name "*.dll" | sort

echo
echo "部署完成！"

package com.ylzx.annotation.domain;

import java.io.Serial;

import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注项目分类关联对象 annotation_project_categories
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationProjectCategories extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 关联主键 */
    private Long id;

    /** 标注项目主键 */
    @Excel(name = "标注项目主键")
    private Long projectId;

    /** 标注分类主键 */
    @Excel(name = "标注分类主键")
    private Long categoryId;

    /** 关联状态 (active: 激活, inactive: 停用) */
    @Excel(name = "关联状态")
    private String status;
}

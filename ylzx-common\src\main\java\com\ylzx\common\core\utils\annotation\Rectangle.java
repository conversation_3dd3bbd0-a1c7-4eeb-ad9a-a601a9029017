package com.ylzx.common.core.utils.annotation;

import lombok.Getter;

import java.util.Objects;

/**
 * 表示一个与坐标轴平行的矩形.
 * 由左上角和右下角两个点定义.
 *
 * <AUTHOR>
 */
@Getter
public class Rectangle implements Shape {

    private final Point topLeft;
    private final Point bottomRight;

    public Rectangle(Point p1, Point p2) {
        // 标准化，确保 p1 是左上角，p2 是右下角
        this.topLeft = new Point(Math.min(p1.getX(), p2.getX()), Math.min(p1.getY(), p2.getY()));
        this.bottomRight = new Point(Math.max(p1.getX(), p2.getX()), Math.max(p1.getY(), p2.getY()));
    }

    @Override
    public String toWkt() {
        return String.format("%s, %s", topLeft.toString(), bottomRight.toString());
    }

    public double getWidth() {
        return bottomRight.getX() - topLeft.getX();
    }

    public double getHeight() {
        return bottomRight.getY() - topLeft.getY();
    }

    @Override
    public String toString() {
        return toWkt();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Rectangle rectangle = (Rectangle) o;
        return Objects.equals(topLeft, rectangle.topLeft) &&
                Objects.equals(bottomRight, rectangle.bottomRight);
    }

    @Override
    public int hashCode() {
        return Objects.hash(topLeft, bottomRight);
    }
} 
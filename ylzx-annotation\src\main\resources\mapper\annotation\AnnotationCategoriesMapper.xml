<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationCategoriesMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationCategories" id="AnnotationCategoriesResult">
        <result property="categoryId"    column="category_id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="description"    column="description"    />
        <result property="reviewRatio"    column="review_ratio"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationCategoriesVo">
        select category_id, name, code, description, review_ratio, create_time, update_time, create_by, update_by from annotation_categories
    </sql>

    <select id="selectAnnotationCategoriesList" parameterType="AnnotationCategories" resultMap="AnnotationCategoriesResult">
        <include refid="selectAnnotationCategoriesVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like '%' || #{name} || '%'</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="reviewRatio != null"> and review_ratio = #{reviewRatio}</if>
        </where>
    </select>
    
    <select id="selectAnnotationCategoriesByCategoryId" parameterType="Long" resultMap="AnnotationCategoriesResult">
        <include refid="selectAnnotationCategoriesVo"/>
        where category_id = #{categoryId}
    </select>

    <insert id="insertAnnotationCategories" parameterType="AnnotationCategories" useGeneratedKeys="true" keyProperty="categoryId">
        insert into annotation_categories
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="reviewRatio != null">review_ratio,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name,jdbcType=VARCHAR},</if>
            <if test="code != null">#{code,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="reviewRatio != null">#{reviewRatio,jdbcType=NUMERIC},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
         </trim>
    </insert>

    <update id="updateAnnotationCategories" parameterType="AnnotationCategories">
        update annotation_categories
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name,jdbcType=VARCHAR},</if>
            <if test="code != null">code = #{code,jdbcType=VARCHAR},</if>
            <if test="description != null">description = #{description,jdbcType=VARCHAR},</if>
            <if test="reviewRatio != null">review_ratio = #{reviewRatio,jdbcType=NUMERIC},</if>
            <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">create_by = #{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">update_by = #{updateBy,jdbcType=VARCHAR},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteAnnotationCategoriesByCategoryId" parameterType="Long">
        delete from annotation_categories where category_id = #{categoryId}
    </delete>

    <delete id="deleteAnnotationCategoriesByCategoryIds" parameterType="String">
        delete from annotation_categories where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
</mapper>
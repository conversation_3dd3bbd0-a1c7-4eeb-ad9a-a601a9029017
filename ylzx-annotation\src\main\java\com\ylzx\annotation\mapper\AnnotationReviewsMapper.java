package com.ylzx.annotation.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationReviews;
import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标注审核Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationReviewsMapper extends BaseMapper<AnnotationReviews>
{
    /**
     * 查询标注审核
     * 
     * @param reviewId 标注审核主键
     * @return 标注审核
     */
    AnnotationReviews selectAnnotationReviewsByReviewId(Long reviewId);

    /**
     * 查询标注审核列表
     * 
     * @param annotationReviews 标注审核
     * @return 标注审核集合
     */
    List<AnnotationReviews> selectAnnotationReviewsList(AnnotationReviews annotationReviews);

    /**
     * 新增标注审核
     * 
     * @param annotationReviews 标注审核
     * @return 结果
     */
    int insertAnnotationReviews(AnnotationReviews annotationReviews);

    /**
     * 修改标注审核
     * 
     * @param annotationReviews 标注审核
     * @return 结果
     */
    int updateAnnotationReviews(AnnotationReviews annotationReviews);

    /**
     * 删除标注审核
     * 
     * @param reviewId 标注审核主键
     * @return 结果
     */
    int deleteAnnotationReviewsByReviewId(Long reviewId);

    /**
     * 批量删除标注审核
     *
     * @param reviewIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationReviewsByReviewIds(Long[] reviewIds);

    /**
     * 根据标注人ID查询审核记录
     *
     * @param annotatorId 标注人ID
     * @return 审核记录列表
     */
    List<AnnotationReviews> selectAnnotationReviewsByAnnotatorId(@Param("annotatorId") Long annotatorId);

    /**
     * 根据图片ID查询审核记录
     *
     * @param imageId 图片ID
     * @return 审核记录列表
     */
    List<AnnotationReviews> selectAnnotationReviewsByImageId(@Param("imageId") Long imageId);

    /**
     * 根据标注人ID和状态查询审核记录
     *
     * @param annotatorId 标注人ID
     * @param status 审核状态
     * @return 审核记录列表
     */
    List<AnnotationReviews> selectAnnotationReviewsByAnnotatorIdAndStatus(@Param("annotatorId") Long annotatorId, @Param("status") AnnotationStatus status);

    /**
     * 统计标注人的审核情况
     *
     * @param annotatorId 标注人ID
     * @return 统计结果 Map，包含各种状态的数量
     */
    Map<String, Object> countReviewStatsByAnnotatorId(@Param("annotatorId") Long annotatorId);

    /**
     * 根据条件查询标注记录用于抽审
     *
     * @param categoryId 标注分类ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 标注记录列表
     */
    List<AnnotationAnnotations> selectAnnotationsForReview(@Param("categoryId") Long categoryId,
                                                          @Param("startTime") java.time.LocalDateTime startTime,
                                                          @Param("endTime") java.time.LocalDateTime endTime,
                                                          @Param("limit") Integer limit);

    /**
     * 批量插入审核记录
     *
     * @param reviewList 审核记录列表
     * @return 插入数量
     */
    int batchInsertReviews(@Param("reviewList") List<AnnotationReviews> reviewList);

    /**
     * 检查标注记录是否已存在审核记录
     *
     * @param annotationId 标注ID
     * @return 数量
     */
    int countByAnnotationId(@Param("annotationId") Long annotationId);

    /**
     * 查询可领取的审核任务
     *
     * @param categoryId 标注分类ID (可选)
     * @param auditor 审核员用户名
     * @param limit 限制数量
     * @return 审核任务列表
     */
    List<AnnotationReviews> selectAvailableReviewTasks(@Param("categoryId") Long categoryId,
                                                      @Param("auditor") String auditor,
                                                      @Param("limit") int limit);

    /**
     * 批量更新审核任务的审核员
     *
     * @param reviewIds 审核记录ID列表
     * @param auditor 审核员用户名
     * @param claimTime 领取时间
     * @return 更新数量
     */
    int batchUpdateReviewer(@Param("reviewIds") List<Long> reviewIds,
                           @Param("auditor") String auditor,
                           @Param("claimTime") java.time.LocalDateTime claimTime);
}

# 本地库文件目录结构

这个目录包含了不同平台的本地库文件，支持跨平台部署。

## 目录结构

```
native/
├── dataset_export.dll          # Windows 通用版本
├── libdataset_export.so        # Linux 通用版本  
├── libdataset_export.dylib     # macOS 通用版本
├── amd64/                      # x86_64 架构特定版本
│   ├── dataset_export.dll      # Windows x64
│   ├── libdataset_export.so    # Linux x64
│   └── libdataset_export.dylib # macOS x64
├── aarch64/                    # ARM64 架构特定版本
│   ├── dataset_export.dll      # Windows ARM64
│   ├── libdataset_export.so    # Linux ARM64
│   └── libdataset_export.dylib # macOS ARM64 (Apple Silicon)
└── README.md                   # 本文件
```

## 加载优先级

Java代码会按以下优先级加载库文件：

1. **架构特定版本**: `/native/{arch}/{library_name}`
   - 例如: `/native/amd64/dataset_export.dll`
   
2. **通用版本**: `/native/{library_name}`
   - 例如: `/native/dataset_export.dll`

## 支持的平台

### Windows
- **文件名**: `dataset_export.dll`
- **架构**: amd64 (x86_64), aarch64 (ARM64)

### Linux  
- **文件名**: `libdataset_export.so`
- **架构**: amd64 (x86_64), aarch64 (ARM64)

### macOS
- **文件名**: `libdataset_export.dylib`
- **架构**: amd64 (Intel), aarch64 (Apple Silicon)

## 编译说明

### Windows (MinGW)
```bash
cd native/build
cmake .. -G "MinGW Makefiles"
mingw32-make
# 生成: dataset_export.dll
```

### Linux
```bash
cd native/build  
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
# 生成: libdataset_export.so
```

### macOS
```bash
cd native/build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(sysctl -n hw.ncpu)
# 生成: libdataset_export.dylib
```

## 部署建议

1. **开发环境**: 只需要当前平台的库文件
2. **生产环境**: 建议包含所有目标平台的库文件
3. **Docker部署**: 只需要Linux版本
4. **跨平台分发**: 包含所有平台版本

## 注意事项

- 库文件必须与目标平台的架构匹配
- 静态编译的库文件更容易部署（无需额外依赖）
- 确保库文件具有正确的执行权限（Linux/macOS）

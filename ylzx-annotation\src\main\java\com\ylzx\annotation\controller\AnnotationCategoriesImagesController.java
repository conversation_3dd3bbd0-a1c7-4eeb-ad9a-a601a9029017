package com.ylzx.annotation.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.ylzx.annotation.service.AnnotationCategoriesImagesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.domain.vo.ImageWithAnnotationsVo;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 标注项目图片Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注项目图片管理")
@RestController
//@RequestMapping("/annotation/projectImages")
@RequestMapping("/annotation/CategoriesImages")
public class AnnotationCategoriesImagesController extends BaseController
{
    @Resource
    private AnnotationCategoriesImagesService annotationCategoriesImagesService;

    /**
     * 查询标注项目图片列表
     */
    @ApiOperation("查询标注项目图片列表")
    @PreAuthorize("@ss.hasPermi('system:images:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") AnnotationCategoriesImages annotationCategoriesImages)
    {
        startPage();
        List<AnnotationCategoriesImages> list = annotationCategoriesImagesService.selectAnnotationCategoriesImagesList(annotationCategoriesImages);
        return getDataTable(list);
    }

    /**
     * 导出标注项目图片列表
     */
    @PreAuthorize("@ss.hasPermi('system:images:export')")
    @Log(title = "标注项目图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationCategoriesImages annotationCategoriesImages)
    {
        List<AnnotationCategoriesImages> list = annotationCategoriesImagesService.selectAnnotationCategoriesImagesList(annotationCategoriesImages);
        ExcelUtil<AnnotationCategoriesImages> util = new ExcelUtil<AnnotationCategoriesImages>(AnnotationCategoriesImages.class);
        util.exportExcel(response, list, "标注项目图片数据");
    }

    /**
     * 获取标注项目图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:images:query')")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId)
    {
        return success(annotationCategoriesImagesService.selectAnnotationCategoriesImagesByCategoryId(projectId));
    }

    /**
     * 新增标注项目图片
     */
    @PreAuthorize("@ss.hasPermi('system:images:add')")
    @Log(title = "标注项目图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationCategoriesImages annotationCategoriesImages)
    {
        return toAjax(annotationCategoriesImagesService.insertAnnotationCategoriesImages(annotationCategoriesImages));
    }

    /**
     * 修改标注项目图片
     */
    @PreAuthorize("@ss.hasPermi('system:images:edit')")
    @Log(title = "标注项目图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationCategoriesImages annotationCategoriesImages)
    {
        return toAjax(annotationCategoriesImagesService.updateAnnotationCategoriesImages(annotationCategoriesImages));
    }

    /**
     * 删除标注项目图片
     */
    @PreAuthorize("@ss.hasPermi('system:images:remove')")
    @Log(title = "标注项目图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable("projectIds") Long[] projectIds)
    {
        return toAjax(annotationCategoriesImagesService.deleteAnnotationCategoriesImagesByCategoryIds(projectIds));
    }

    /**
     * 为当前用户领取标注任务
     */
    @PreAuthorize("@ss.hasPermi('annotation:images:claim')")
    @Log(title = "领取标注任务", businessType = BusinessType.UPDATE)
    @PostMapping("/claim/{categoryId}")
    public AjaxResult claimTasks(@PathVariable Long categoryId, @RequestParam(defaultValue = "10") int limit) {
        List<AnnotationCategoriesImages> claimedImages = annotationCategoriesImagesService.claimImagesForAnnotation(categoryId, null, limit);
        return success(claimedImages);
    }

    /**
     * 为当前用户领取标注任务（返回详细信息，包含图片和标注）
     */
    @ApiOperation("为当前用户领取标注任务（返回详细信息）")
    @PreAuthorize("@ss.hasPermi('annotation:images:claim')")
    @Log(title = "领取标注任务", businessType = BusinessType.UPDATE)
    @PostMapping("/claim-with-details/{categoryId}")
    public AjaxResult claimTasksWithDetails(@ApiParam("分类ID") @PathVariable Long categoryId,
                                          @ApiParam("领取数量") @RequestParam(defaultValue = "10") int limit) {
        List<ImageWithAnnotationsVo> claimedImages = annotationCategoriesImagesService.claimImagesForAnnotationWithDetails(categoryId, null, limit);
        return success(claimedImages);
    }

    /**
     * 获取当前用户未完成的标注任务
     */
    @ApiOperation("获取当前用户未完成的标注任务")
    @PreAuthorize("@ss.hasPermi('annotation:images:myTasks')")
    @GetMapping("/my-tasks/{categoryId}")
    public AjaxResult getMyUnfinishedTasks(@ApiParam("分类ID") @PathVariable Long categoryId) {
        List<AnnotationCategoriesImages> unfinishedTasks = annotationCategoriesImagesService.getMyUnfinishedTasks(categoryId);
        return success(unfinishedTasks);
    }

    /**
     * 释放当前用户所有进行中的任务
     */
    @ApiOperation("释放当前用户所有进行中的任务")
    @PreAuthorize("@ss.hasPermi('annotation:images:release')")
    @Log(title = "释放我的任务", businessType = BusinessType.UPDATE)
    @PostMapping("/my-tasks/release/{categoryId}")
    public AjaxResult releaseMyTasks(@ApiParam("分类ID") @PathVariable Long categoryId) {
        return toAjax(annotationCategoriesImagesService.releaseMyTasks(categoryId));
    }

    /**
     * 获取当前用户的任务列表（返回详细信息，包含图片和标注）
     */
    @ApiOperation("获取当前用户的任务列表（返回详细信息）")
    @PreAuthorize("@ss.hasPermi('annotation:images:list')")
    @GetMapping("/my-tasks-with-details")
    public AjaxResult getMyTasksWithDetails(@ApiParam("分类ID，可选") @RequestParam(required = false) Long categoryId,
                                          @ApiParam("状态列表，逗号分隔") @RequestParam(required = false) String statuses) {
        List<com.ylzx.annotation.domain.enums.AnnotationStatus> statusList = null;
        if (statuses != null && !statuses.trim().isEmpty()) {
            statusList = Arrays.stream(statuses.split(","))
                    .map(String::trim)
                    .map(com.ylzx.annotation.domain.enums.AnnotationStatus::valueOf)
                    .collect(Collectors.toList());
        }

        List<ImageWithAnnotationsVo> tasks = annotationCategoriesImagesService.getMyTasksByStatusesWithDetails(categoryId, statusList);
        return success(tasks);
    }

    /**
     * 获取单个任务的详细信息（包含图片和标注）
     */
    @ApiOperation("获取单个任务的详细信息")
    @PreAuthorize("@ss.hasPermi('annotation:images:query')")
    @GetMapping("/task-details/{taskId}")
    public AjaxResult getTaskDetails(@ApiParam("任务ID") @PathVariable Long taskId) {
        ImageWithAnnotationsVo taskDetails = annotationCategoriesImagesService.getTaskWithDetails(taskId);
        if (taskDetails == null) {
            return error("任务不存在");
        }
        return success(taskDetails);
    }
}

package com.ylzx.annotation.service;

import java.util.List;
import java.util.Map;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;

/**
 * 标注图片来源Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationImageSourcesService extends IService<AnnotationImageSources>
{

    /**
     * 查询标注图片来源
     * 
     * @param sourceId 标注图片来源主键
     * @return 标注图片来源
     */
    AnnotationImageSources selectAnnotationImageSourcesBySourceId(Long sourceId);

    /**
     * 查询标注图片来源列表
     * 
     * @param annotationImageSources 标注图片来源
     * @return 标注图片来源集合
     */
    List<AnnotationImageSources> selectAnnotationImageSourcesList(AnnotationImageSources annotationImageSources);

    /**
     * 批量删除标注图片来源
     *
     * @param sourceIds 需要删除的标注图片来源主键
     * @return 结果
     */
    int deleteAnnotationImageSourcesBySourceIds(Long[] sourceIds);

    /**
     * 删除标注图片来源信息
     *
     * @param sourceId 标注图片来源主键
     * @return 结果
     */
    int deleteAnnotationImageSourcesBySourceId(Long sourceId);

    /**
     * 通过上传压缩文件来创建新的图片源.
     *
     * @param file 上传的压缩文件 (zip, tar.gz)
     * @return 创建的数据源实体
     * @throws IOException 如果文件处理失败
     */
    AnnotationImageSources uploadSource(MultipartFile file) throws IOException;

    /**
     * 通过上传压缩文件来创建新的图片源，并关联到指定分类.
     *
     * @param file 上传的压缩文件 (zip, tar.gz)
     * @param categoryId 分类ID，如果不为null则自动处理图片并关联到该分类
     * @return 创建的数据源实体
     * @throws IOException 如果文件处理失败
     */
    AnnotationImageSources uploadSourceWithCategory(MultipartFile file, Long categoryId) throws IOException;

    /**
     * 扫描指定目录并导入新的图片源.
     *
     * @throws IOException 如果扫描或文件处理失败
     */
    void scanAndImportSources() throws IOException;

    /**
     * 扫描并导入源文件，同时自动处理图片
     * @param categoryId 分类ID，如果指定则自动处理图片并关联到该分类
     * @throws IOException IO异常
     */
    void scanAndImportSourcesWithImageProcessing(Long categoryId) throws IOException;

    /**
     * 获取扫描目录中的压缩文件列表
     * @return 压缩文件信息列表
     */
    List<Map<String, Object>> getScanDirectoryFiles();

    /**
     * 手动处理指定的扫描文件
     * @param filename 文件名
     * @param categoryId 分类ID，可选
     * @return 处理结果
     * @throws IOException IO异常
     */
    AnnotationImageSources processScanFile(String filename, Long categoryId) throws IOException;
}

package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationCategoriesSources;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注分类数据源关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
public interface AnnotationCategoriesSourcesService extends IService<AnnotationCategoriesSources>
{
    /**
     * 查询标注分类数据源关联列表
     * 
     * @param annotationCategoriesSources 标注分类数据源关联
     * @return 标注分类数据源关联集合
     */
    List<AnnotationCategoriesSources> selectAnnotationCategoriesSourcesList(AnnotationCategoriesSources annotationCategoriesSources);

    /**
     * 创建分类与数据源的关联
     * 
     * @param categoryId 分类ID
     * @param sourceId 数据源ID
     * @param userId 操作用户ID
     * @return 创建的关联记录
     */
    AnnotationCategoriesSources associateCategoryWithSource(Long categoryId, Long sourceId, Long userId);

    /**
     * 检查分类和数据源是否已关联
     * 
     * @param categoryId 分类ID
     * @param sourceId 数据源ID
     * @return 是否已关联
     */
    boolean isAssociated(Long categoryId, Long sourceId);

    /**
     * 根据分类ID查询关联的数据源ID列表
     * 
     * @param categoryId 分类ID
     * @return 数据源ID列表
     */
    List<Long> getSourceIdsByCategoryId(Long categoryId);

    /**
     * 根据数据源ID查询关联的分类ID列表
     * 
     * @param sourceId 数据源ID
     * @return 分类ID列表
     */
    List<Long> getCategoryIdsBySourceId(Long sourceId);

    /**
     * 批量创建分类与数据源的关联
     * 
     * @param categoryId 分类ID
     * @param sourceIds 数据源ID列表
     * @param userId 操作用户ID
     * @return 创建的关联记录数
     */
    int batchAssociateCategoryWithSources(Long categoryId, List<Long> sourceIds, Long userId);

    /**
     * 删除分类的所有数据源关联
     * 
     * @param categoryId 分类ID
     * @return 删除的记录数
     */
    int removeAllSourcesFromCategory(Long categoryId);

    /**
     * 删除数据源的所有分类关联
     * 
     * @param sourceId 数据源ID
     * @return 删除的记录数
     */
    int removeAllCategoriesFromSource(Long sourceId);

    /**
     * 移除特定的分类与数据源关联
     * 
     * @param categoryId 分类ID
     * @param sourceId 数据源ID
     * @return 是否成功移除
     */
    boolean removeAssociation(Long categoryId, Long sourceId);
}

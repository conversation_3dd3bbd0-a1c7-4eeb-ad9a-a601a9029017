#include "dataset_export_jni.h"
#include "coco_exporter.h"
#include "voc_exporter.h"
#include "yolo_exporter.h"
#include "image_processor.h"
#include <iostream>
#include <stdexcept>

// 版本信息
const char* LIBRARY_VERSION = "1.0.0";

// JNI方法实现
JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportCOCO
  (JNIEnv *env, jobject obj, jobject jconfig, jobjectArray jimages, jstring jcategories) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        std::vector<ImageInfo> images = javaImagesToNative(env, jimages);
        
        const char* categoriesStr = env->GetStringUTFChars(jcategories, nullptr);
        std::string categories(categoriesStr);
        env->ReleaseStringUTFChars(jcategories, categoriesStr);
        
        COCOExporter exporter;
        ExportResult result = exporter.exportDataset(config, images, categories);
        
        return nativeResultToJava(env, result);
    } catch (const std::exception& e) {
        ExportResult errorResult;
        errorResult.success = false;
        errorResult.message = std::string("COCO导出失败: ") + e.what();
        errorResult.processedCount = 0;
        return nativeResultToJava(env, errorResult);
    }
}

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportVOC
  (JNIEnv *env, jobject obj, jobject jconfig, jobjectArray jimages, jstring jcategories) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        std::vector<ImageInfo> images = javaImagesToNative(env, jimages);
        
        const char* categoriesStr = env->GetStringUTFChars(jcategories, nullptr);
        std::string categories(categoriesStr);
        env->ReleaseStringUTFChars(jcategories, categoriesStr);
        
        VOCExporter exporter;
        ExportResult result = exporter.exportDataset(config, images, categories);
        
        return nativeResultToJava(env, result);
    } catch (const std::exception& e) {
        ExportResult errorResult;
        errorResult.success = false;
        errorResult.message = std::string("VOC导出失败: ") + e.what();
        errorResult.processedCount = 0;
        return nativeResultToJava(env, errorResult);
    }
}

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportYOLO
  (JNIEnv *env, jobject obj, jobject jconfig, jobjectArray jimages, jstring jcategories) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        std::vector<ImageInfo> images = javaImagesToNative(env, jimages);
        
        const char* categoriesStr = env->GetStringUTFChars(jcategories, nullptr);
        std::string categories(categoriesStr);
        env->ReleaseStringUTFChars(jcategories, categoriesStr);
        
        YOLOExporter exporter;
        ExportResult result = exporter.exportDataset(config, images, categories);
        
        return nativeResultToJava(env, result);
    } catch (const std::exception& e) {
        ExportResult errorResult;
        errorResult.success = false;
        errorResult.message = std::string("YOLO导出失败: ") + e.what();
        errorResult.processedCount = 0;
        return nativeResultToJava(env, errorResult);
    }
}

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_applyImageTransformations
  (JNIEnv *env, jobject obj, jstring jimagePath, jstring joutputPath, jstring jtransformations) {
    
    try {
        const char* imagePathStr = env->GetStringUTFChars(jimagePath, nullptr);
        const char* outputPathStr = env->GetStringUTFChars(joutputPath, nullptr);
        const char* transformationsStr = env->GetStringUTFChars(jtransformations, nullptr);
        
        std::string imagePath(imagePathStr);
        std::string outputPath(outputPathStr);
        std::string transformations(transformationsStr);
        
        env->ReleaseStringUTFChars(jimagePath, imagePathStr);
        env->ReleaseStringUTFChars(joutputPath, outputPathStr);
        env->ReleaseStringUTFChars(jtransformations, transformationsStr);
        
        ImageProcessor processor;
        return processor.applyTransformations(imagePath, outputPath, transformations);
    } catch (const std::exception& e) {
        std::cerr << "图像变换失败: " << e.what() << std::endl;
        return JNI_FALSE;
    }
}

JNIEXPORT jstring JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_validateConfig
  (JNIEnv *env, jobject obj, jobject jconfig) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        
        // 基本验证逻辑
        if (config.outputPath.empty()) {
            return env->NewStringUTF("输出路径不能为空");
        }
        
        if (config.targetWidth <= 0 || config.targetHeight <= 0) {
            return env->NewStringUTF("目标尺寸必须大于0");
        }
        
        return env->NewStringUTF("配置验证通过");
    } catch (const std::exception& e) {
        return env->NewStringUTF(("配置验证失败: " + std::string(e.what())).c_str());
    }
}

JNIEXPORT jobjectArray JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_getSupportedFormats
  (JNIEnv *env, jobject obj) {
    
    const char* formats[] = {"coco", "voc", "yolo"};
    int formatCount = sizeof(formats) / sizeof(formats[0]);
    
    jclass stringClass = env->FindClass("java/lang/String");
    jobjectArray result = env->NewObjectArray(formatCount, stringClass, nullptr);
    
    for (int i = 0; i < formatCount; i++) {
        jstring format = env->NewStringUTF(formats[i]);
        env->SetObjectArrayElement(result, i, format);
        env->DeleteLocalRef(format);
    }
    
    return result;
}

JNIEXPORT jstring JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_getLibraryVersion
  (JNIEnv *env, jobject obj) {
    return env->NewStringUTF(LIBRARY_VERSION);
}

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_cleanupTempFiles
  (JNIEnv *env, jobject obj, jstring jtempDir) {
    
    try {
        const char* tempDirStr = env->GetStringUTFChars(jtempDir, nullptr);
        std::string tempDir(tempDirStr);
        env->ReleaseStringUTFChars(jtempDir, tempDirStr);
        
        // 实现临时文件清理逻辑
        // 这里可以添加具体的文件系统操作
        
        return JNI_TRUE;
    } catch (const std::exception& e) {
        std::cerr << "清理临时文件失败: " << e.what() << std::endl;
        return JNI_FALSE;
    }
}

// C++辅助函数实现
ExportConfig javaConfigToNative(JNIEnv* env, jobject jconfig) {
    ExportConfig config;

    jclass configClass = env->GetObjectClass(jconfig);

    // 获取字段ID
    jfieldID exportFormatField = env->GetFieldID(configClass, "exportFormat", "Ljava/lang/String;");
    jfieldID outputPathField = env->GetFieldID(configClass, "outputPath", "Ljava/lang/String;");
    jfieldID targetWidthField = env->GetFieldID(configClass, "targetWidth", "I");
    jfieldID targetHeightField = env->GetFieldID(configClass, "targetHeight", "I");
    jfieldID transformationsField = env->GetFieldID(configClass, "transformations", "Ljava/lang/String;");
    jfieldID grayscaleField = env->GetFieldID(configClass, "grayscale", "Z");
    jfieldID enableMaskField = env->GetFieldID(configClass, "enableMask", "Z");
    jfieldID rotationAngleField = env->GetFieldID(configClass, "rotationAngle", "D");
    jfieldID scaleRatioField = env->GetFieldID(configClass, "scaleRatio", "D");

    // 获取字段值
    jstring jexportFormat = (jstring)env->GetObjectField(jconfig, exportFormatField);
    jstring joutputPath = (jstring)env->GetObjectField(jconfig, outputPathField);
    jstring jtransformations = (jstring)env->GetObjectField(jconfig, transformationsField);

    // 转换字符串
    if (jexportFormat) {
        const char* formatStr = env->GetStringUTFChars(jexportFormat, nullptr);
        config.format = stringToFormat(std::string(formatStr));
        env->ReleaseStringUTFChars(jexportFormat, formatStr);
    }

    if (joutputPath) {
        const char* pathStr = env->GetStringUTFChars(joutputPath, nullptr);
        config.outputPath = std::string(pathStr);
        env->ReleaseStringUTFChars(joutputPath, pathStr);
    }

    if (jtransformations) {
        const char* transStr = env->GetStringUTFChars(jtransformations, nullptr);
        config.transformations = std::string(transStr);
        env->ReleaseStringUTFChars(jtransformations, transStr);
    }

    // 获取基本类型字段
    config.targetWidth = env->GetIntField(jconfig, targetWidthField);
    config.targetHeight = env->GetIntField(jconfig, targetHeightField);
    config.grayscale = env->GetBooleanField(jconfig, grayscaleField);
    config.enableMask = env->GetBooleanField(jconfig, enableMaskField);
    config.rotationAngle = env->GetDoubleField(jconfig, rotationAngleField);
    config.scaleRatio = env->GetDoubleField(jconfig, scaleRatioField);

    return config;
}

std::vector<ImageInfo> javaImagesToNative(JNIEnv* env, jobjectArray jimages) {
    std::vector<ImageInfo> images;

    jsize length = env->GetArrayLength(jimages);
    images.reserve(length);

    for (jsize i = 0; i < length; i++) {
        jobject jimage = env->GetObjectArrayElement(jimages, i);
        jclass imageClass = env->GetObjectClass(jimage);

        // 获取字段ID
        jfieldID imageIdField = env->GetFieldID(imageClass, "imageId", "J");
        jfieldID imagePathField = env->GetFieldID(imageClass, "imagePath", "Ljava/lang/String;");
        jfieldID annotationDataField = env->GetFieldID(imageClass, "annotationData", "Ljava/lang/String;");
        jfieldID datasetTypeField = env->GetFieldID(imageClass, "datasetType", "Ljava/lang/String;");
        jfieldID originalWidthField = env->GetFieldID(imageClass, "originalWidth", "I");
        jfieldID originalHeightField = env->GetFieldID(imageClass, "originalHeight", "I");

        ImageInfo info;
        info.imageId = env->GetLongField(jimage, imageIdField);
        info.originalWidth = env->GetIntField(jimage, originalWidthField);
        info.originalHeight = env->GetIntField(jimage, originalHeightField);

        // 获取字符串字段
        jstring jimagePath = (jstring)env->GetObjectField(jimage, imagePathField);
        jstring jannotationData = (jstring)env->GetObjectField(jimage, annotationDataField);
        jstring jdatasetType = (jstring)env->GetObjectField(jimage, datasetTypeField);

        if (jimagePath) {
            const char* pathStr = env->GetStringUTFChars(jimagePath, nullptr);
            info.imagePath = std::string(pathStr);
            env->ReleaseStringUTFChars(jimagePath, pathStr);
        }

        if (jannotationData) {
            const char* dataStr = env->GetStringUTFChars(jannotationData, nullptr);
            info.annotationData = std::string(dataStr);
            env->ReleaseStringUTFChars(jannotationData, dataStr);
        }

        if (jdatasetType) {
            const char* typeStr = env->GetStringUTFChars(jdatasetType, nullptr);
            info.datasetType = stringToDatasetType(std::string(typeStr));
            env->ReleaseStringUTFChars(jdatasetType, typeStr);
        }

        images.push_back(info);
        env->DeleteLocalRef(jimage);
    }

    return images;
}

jobject nativeResultToJava(JNIEnv* env, const ExportResult& result) {
    jclass resultClass = env->FindClass("com/ylzx/annotation/jni/DatasetExportNative$ExportResult");
    jmethodID constructor = env->GetMethodID(resultClass, "<init>", "()V");
    jobject jresult = env->NewObject(resultClass, constructor);

    // 设置字段
    jfieldID successField = env->GetFieldID(resultClass, "success", "Z");
    jfieldID messageField = env->GetFieldID(resultClass, "message", "Ljava/lang/String;");
    jfieldID processedCountField = env->GetFieldID(resultClass, "processedCount", "I");
    jfieldID outputPathField = env->GetFieldID(resultClass, "outputPath", "Ljava/lang/String;");

    env->SetBooleanField(jresult, successField, result.success);
    env->SetIntField(jresult, processedCountField, result.processedCount);

    jstring jmessage = env->NewStringUTF(result.message.c_str());
    jstring joutputPath = env->NewStringUTF(result.outputPath.c_str());

    env->SetObjectField(jresult, messageField, jmessage);
    env->SetObjectField(jresult, outputPathField, joutputPath);

    return jresult;
}

package com.ylzx.flowable.config;

import org.flowable.engine.impl.db.DbIdGenerator;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.context.annotation.Configuration;

/**
 * 扩展流程配置
 * <AUTHOR>
 * @date 2022-12-26 10:24
 */
@Configuration
public class FlowableConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {
    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        engineConfiguration.setActivityFontName("宋体");
        engineConfiguration.setLabelFontName("宋体");
        engineConfiguration.setAnnotationFontName("宋体");
        engineConfiguration.setIdGenerator(new DbIdGenerator());
        
        // 设置Flowable使用的是PostgreSQL数据库
        engineConfiguration.setDatabaseType("postgres");
        // 修改Flowable使用的SQL格式
        engineConfiguration.setDatabaseSchemaUpdate("true");
    }
}

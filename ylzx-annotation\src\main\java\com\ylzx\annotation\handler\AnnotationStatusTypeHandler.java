package com.ylzx.annotation.handler;

import com.ylzx.annotation.domain.enums.AnnotationStatus;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedTypes(AnnotationStatus.class)
public class AnnotationStatusTypeHandler extends BaseTypeHandler<AnnotationStatus> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, AnnotationStatus parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getCode());
    }

    @Override
    public AnnotationStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String code = rs.getString(columnName);
        return code == null ? null : AnnotationStatus.fromCode(code);
    }

    @Override
    public AnnotationStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String code = rs.getString(columnIndex);
        return code == null ? null : AnnotationStatus.fromCode(code);
    }

    @Override
    public AnnotationStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String code = cs.getString(columnIndex);
        return code == null ? null : AnnotationStatus.fromCode(code);
    }
} 
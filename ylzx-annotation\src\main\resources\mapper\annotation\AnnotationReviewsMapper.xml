<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationReviewsMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationReviews" id="AnnotationReviewsResult">
        <result property="reviewId"    column="review_id"    />
        <result property="annotationId"    column="annotation_id"    />
        <result property="annotatorId"    column="annotator_id"    />
        <result property="imageId"    column="image_id"    />
        <result property="status"    column="status"    />
        <result property="auditor"    column="auditor"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="comments"    column="comments"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationReviewsVo">
        select review_id, annotation_id, annotator_id, image_id, status, auditor, audit_time, comments, create_time, update_time, create_by, update_by from annotation_reviews
    </sql>

    <select id="selectAnnotationReviewsList" parameterType="AnnotationReviews" resultMap="AnnotationReviewsResult">
        <include refid="selectAnnotationReviewsVo"/>
        <where>
            <if test="annotationId != null "> and annotation_id = #{annotationId}</if>
            <if test="annotatorId != null "> and annotator_id = #{annotatorId}</if>
            <if test="imageId != null "> and image_id = #{imageId}</if>
            <if test="auditor != null  and auditor != ''"> and auditor like concat('%', #{auditor}, '%')</if>
            <if test="comments != null  and comments != ''"> and comments = #{comments}</if>
        </where>
    </select>
    
    <select id="selectAnnotationReviewsByReviewId" parameterType="Long" resultMap="AnnotationReviewsResult">
        <include refid="selectAnnotationReviewsVo"/>
        where review_id = #{reviewId}
    </select>

    <insert id="insertAnnotationReviews" parameterType="AnnotationReviews" useGeneratedKeys="true" keyProperty="reviewId">
        insert into annotation_reviews
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="annotationId != null">annotation_id,</if>
            <if test="annotatorId != null">annotator_id,</if>
            <if test="imageId != null">image_id,</if>
            <if test="status != null">status,</if>
            <if test="auditor != null and auditor != ''">auditor,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="comments != null">comments,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="annotationId != null">#{annotationId},</if>
            <if test="annotatorId != null">#{annotatorId},</if>
            <if test="imageId != null">#{imageId},</if>
            <if test="status != null">#{status},</if>
            <if test="auditor != null and auditor != ''">#{auditor},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="comments != null">#{comments},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationReviews" parameterType="AnnotationReviews">
        update annotation_reviews
        <trim prefix="SET" suffixOverrides=",">
            <if test="annotationId != null">annotation_id = #{annotationId},</if>
            <if test="annotatorId != null">annotator_id = #{annotatorId},</if>
            <if test="imageId != null">image_id = #{imageId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditor != null and auditor != ''">auditor = #{auditor},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="comments != null">comments = #{comments},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
        </trim>
        where review_id = #{reviewId}
    </update>

    <delete id="deleteAnnotationReviewsByReviewId" parameterType="Long">
        delete from annotation_reviews where review_id = #{reviewId}
    </delete>

    <delete id="deleteAnnotationReviewsByReviewIds" parameterType="String">
        delete from annotation_reviews where review_id in
        <foreach item="reviewId" collection="array" open="(" separator="," close=")">
            #{reviewId}
        </foreach>
    </delete>

    <!-- 根据标注人ID查询审核记录 -->
    <select id="selectAnnotationReviewsByAnnotatorId" resultMap="AnnotationReviewsResult">
        <include refid="selectAnnotationReviewsVo"/>
        WHERE annotator_id = #{annotatorId}
        ORDER BY audit_time DESC
    </select>

    <!-- 根据图片ID查询审核记录 -->
    <select id="selectAnnotationReviewsByImageId" resultMap="AnnotationReviewsResult">
        <include refid="selectAnnotationReviewsVo"/>
        WHERE image_id = #{imageId}
        ORDER BY audit_time DESC
    </select>

    <!-- 根据标注人ID和状态查询审核记录 -->
    <select id="selectAnnotationReviewsByAnnotatorIdAndStatus" resultMap="AnnotationReviewsResult">
        <include refid="selectAnnotationReviewsVo"/>
        WHERE annotator_id = #{annotatorId} AND status = #{status}
        ORDER BY audit_time DESC
    </select>

    <!-- 统计标注人的审核情况 -->
    <select id="countReviewStatsByAnnotatorId" resultType="java.util.HashMap">
        SELECT
            COUNT(*) as total_reviews,
            SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) as approved_count,
            SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) as rejected_count,
            SUM(CASE WHEN status = '2' THEN 1 ELSE 0 END) as pending_count,
            ROUND(SUM(CASE WHEN status = '3' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as approval_rate,
            ROUND(SUM(CASE WHEN status = '4' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as rejection_rate
        FROM annotation_reviews
        WHERE annotator_id = #{annotatorId}
    </select>

    <!-- 根据条件查询标注记录用于抽审 -->
    <select id="selectAnnotationsForReview" resultType="com.ylzx.annotation.domain.AnnotationAnnotations">
        SELECT
            annotation_id,
            category_id,
            image_id,
            label_id,
            shape_type,
            coordinates,
            status,
            del_flag,
            labeler,
            label_time,
            create_time,
            update_time,
            create_by,
            update_by
        FROM annotation_annotations
        WHERE del_flag = '0'
        AND status = '2' <!-- 待审核状态 -->
        <if test="categoryId != null">
            AND category_id = #{categoryId}
        </if>
        <if test="startTime != null">
            AND label_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND label_time &lt;= #{endTime}
        </if>
        ORDER BY label_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量插入审核记录 -->
    <insert id="batchInsertReviews" parameterType="java.util.List">
        INSERT INTO annotation_reviews (annotation_id, annotator_id, image_id, status, create_time, create_by)
        VALUES
        <foreach collection="reviewList" item="review" separator=",">
            (#{review.annotationId}, #{review.annotatorId}, #{review.imageId}, #{review.status}, NOW(), 'system')
        </foreach>
    </insert>

    <!-- 检查标注记录是否已存在审核记录 -->
    <select id="countByAnnotationId" resultType="int">
        SELECT COUNT(1) FROM annotation_reviews WHERE annotation_id = #{annotationId}
    </select>

    <!-- 查询可领取的审核任务 -->
    <select id="selectAvailableReviewTasks" resultMap="AnnotationReviewsResult">
        <include refid="selectAnnotationReviewsVo"/>
        WHERE status = '2' <!-- 待审核状态 -->
        AND (auditor IS NULL OR auditor = '') <!-- 未分配审核员 -->
        <if test="categoryId != null">
            AND annotation_id IN (
                SELECT annotation_id FROM annotation_annotations
                WHERE category_id = #{categoryId} AND del_flag = '0'
            )
        </if>
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 批量更新审核任务的审核员 -->
    <update id="batchUpdateReviewer">
        UPDATE annotation_reviews
        SET auditor = #{auditor},
            audit_time = #{claimTime},
            update_time = NOW()
        WHERE review_id IN
        <foreach collection="reviewIds" item="reviewId" open="(" separator="," close=")">
            #{reviewId}
        </foreach>
    </update>
</mapper>
package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationExportJobs;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标注导出任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationExportJobsMapper extends BaseMapper<AnnotationExportJobs>
{
    /**
     * 查询标注导出任务
     * 
     * @param jobId 标注导出任务主键
     * @return 标注导出任务
     */
    AnnotationExportJobs selectAnnotationExportJobsByJobId(Long jobId);

    /**
     * 查询标注导出任务列表
     * 
     * @param annotationExportJobs 标注导出任务
     * @return 标注导出任务集合
     */
    List<AnnotationExportJobs> selectAnnotationExportJobsList(AnnotationExportJobs annotationExportJobs);

    /**
     * 新增标注导出任务
     * 
     * @param annotationExportJobs 标注导出任务
     * @return 结果
     */
    int insertAnnotationExportJobs(AnnotationExportJobs annotationExportJobs);

    /**
     * 修改标注导出任务
     * 
     * @param annotationExportJobs 标注导出任务
     * @return 结果
     */
    int updateAnnotationExportJobs(AnnotationExportJobs annotationExportJobs);

    /**
     * 删除标注导出任务
     * 
     * @param jobId 标注导出任务主键
     * @return 结果
     */
    int deleteAnnotationExportJobsByJobId(Long jobId);

    /**
     * 批量删除标注导出任务
     * 
     * @param jobIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationExportJobsByJobIds(Long[] jobIds);
}

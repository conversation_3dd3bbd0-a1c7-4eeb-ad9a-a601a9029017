#ifndef IMAGE_CROP_H
#define IMAGE_CROP_H

#include <string>
#include <vector>

/**
 * 图像裁剪功能头文件
 * 提供基于标注坐标的智能图像裁剪功能
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */

namespace ImageCrop {

    /**
     * 裁剪区域结构
     */
    struct CropRegion {
        int x;          // 裁剪起始X坐标
        int y;          // 裁剪起始Y坐标
        int width;      // 裁剪宽度
        int height;     // 裁剪高度
        
        CropRegion() : x(0), y(0), width(0), height(0) {}
        CropRegion(int x, int y, int w, int h) : x(x), y(y), width(w), height(h) {}
    };

    /**
     * 裁剪配置结构
     */
    struct CropConfig {
        int targetWidth;            // 目标宽度（0表示自动计算）
        int targetHeight;           // 目标高度（0表示自动计算）
        int padding;                // 边距像素
        bool enableRandomPlacement; // 是否启用随机位置放置
        bool maintainAspectRatio;   // 是否保持宽高比
        int backgroundColor[3];     // 背景颜色RGB
        
        CropConfig() : targetWidth(0), targetHeight(0), padding(20), 
                      enableRandomPlacement(true), maintainAspectRatio(true) {
            backgroundColor[0] = 128;
            backgroundColor[1] = 128;
            backgroundColor[2] = 128;
        }
    };

    /**
     * 批量裁剪配置结构
     */
    struct BatchCropConfig {
        std::vector<std::string> sourcePaths;      // 源图像路径列表
        std::vector<std::string> targetPaths;      // 目标图像路径列表
        std::vector<CropRegion> cropRegions;       // 裁剪区域列表
        CropConfig cropConfig;                     // 裁剪配置
        int threadCount;                           // 并行线程数
        
        BatchCropConfig() : threadCount(4) {}
    };

    /**
     * 裁剪结果结构
     */
    struct CropResult {
        bool success;               // 是否成功
        std::string message;        // 结果消息
        std::string outputPath;     // 输出路径
        long processingTimeMs;      // 处理耗时（毫秒）
        
        CropResult() : success(false), processingTimeMs(0) {}
    };

    /**
     * 批量裁剪结果结构
     */
    struct BatchCropResult {
        bool success;                           // 整体是否成功
        std::string message;                    // 结果消息
        int totalCount;                         // 总数量
        int successCount;                       // 成功数量
        int failedCount;                        // 失败数量
        long processingTimeMs;                  // 总处理耗时（毫秒）
        std::vector<std::string> outputPaths;   // 成功的输出路径
        std::vector<std::string> failedPaths;   // 失败的图像路径
        std::vector<std::string> errorMessages; // 对应的错误信息
        
        BatchCropResult() : success(false), totalCount(0), successCount(0), 
                           failedCount(0), processingTimeMs(0) {}
    };

    /**
     * 标注坐标点结构
     */
    struct Point {
        double x;
        double y;
        
        Point() : x(0), y(0) {}
        Point(double x, double y) : x(x), y(y) {}
    };

    /**
     * 边界框结构
     */
    struct BoundingBox {
        int x;
        int y;
        int width;
        int height;
        
        BoundingBox() : x(0), y(0), width(0), height(0) {}
        BoundingBox(int x, int y, int w, int h) : x(x), y(y), width(w), height(h) {}
        
        int right() const { return x + width; }
        int bottom() const { return y + height; }
    };

    /**
     * 图像裁剪器类
     */
    class ImageCropper {
    public:
        ImageCropper();
        ~ImageCropper();

        /**
         * 单张图像裁剪
         * 
         * @param imagePath 源图像路径
         * @param outputPath 输出路径
         * @param cropRegion 裁剪区域
         * @param config 裁剪配置
         * @return 裁剪结果
         */
        CropResult cropImage(const std::string& imagePath, 
                           const std::string& outputPath,
                           const CropRegion& cropRegion, 
                           const CropConfig& config);

        /**
         * 批量图像裁剪
         * 
         * @param batchConfig 批量裁剪配置
         * @return 批量裁剪结果
         */
        BatchCropResult batchCropImages(const BatchCropConfig& batchConfig);

        /**
         * 智能裁剪：根据标注坐标自动计算裁剪区域
         * 
         * @param imagePath 源图像路径
         * @param outputPath 输出路径
         * @param annotationCoordinates 标注坐标JSON字符串
         * @param config 裁剪配置
         * @return 裁剪结果
         */
        CropResult smartCropByAnnotations(const std::string& imagePath,
                                        const std::string& outputPath,
                                        const std::string& annotationCoordinates,
                                        const CropConfig& config);

        /**
         * 批量智能裁剪
         * 
         * @param imagePaths 源图像路径列表
         * @param outputPaths 输出路径列表
         * @param annotationCoordinatesArray 标注坐标JSON字符串列表
         * @param config 裁剪配置
         * @param threadCount 并行线程数
         * @return 批量裁剪结果
         */
        BatchCropResult batchSmartCropByAnnotations(const std::vector<std::string>& imagePaths,
                                                  const std::vector<std::string>& outputPaths,
                                                  const std::vector<std::string>& annotationCoordinatesArray,
                                                  const CropConfig& config,
                                                  int threadCount);

    private:
        /**
         * 解析标注坐标JSON
         * 
         * @param annotationJson 标注坐标JSON字符串
         * @param imageWidth 图像宽度
         * @param imageHeight 图像高度
         * @return 计算出的边界框
         */
        BoundingBox parseAnnotationCoordinates(const std::string& annotationJson,
                                             int imageWidth, int imageHeight);

        /**
         * 计算裁剪区域
         * 
         * @param boundingBox 标注边界框
         * @param imageWidth 图像宽度
         * @param imageHeight 图像高度
         * @param config 裁剪配置
         * @return 计算出的裁剪区域
         */
        CropRegion calculateCropRegion(const BoundingBox& boundingBox,
                                     int imageWidth, int imageHeight,
                                     const CropConfig& config);

        /**
         * 计算随机位置
         * 
         * @param objectPos 对象位置
         * @param objectSize 对象大小
         * @param cropSize 裁剪大小
         * @param imageSize 图像大小
         * @return 随机位置
         */
        int calculateRandomPosition(int objectPos, int objectSize, int cropSize, int imageSize);

        /**
         * 执行实际的图像裁剪操作
         * 
         * @param imagePath 源图像路径
         * @param outputPath 输出路径
         * @param cropRegion 裁剪区域
         * @param config 裁剪配置
         * @return 是否成功
         */
        bool performImageCrop(const std::string& imagePath,
                            const std::string& outputPath,
                            const CropRegion& cropRegion,
                            const CropConfig& config);

        /**
         * 验证输入参数
         * 
         * @param imagePath 图像路径
         * @param outputPath 输出路径
         * @param cropRegion 裁剪区域
         * @return 是否有效
         */
        bool validateInputs(const std::string& imagePath,
                          const std::string& outputPath,
                          const CropRegion& cropRegion);

        /**
         * 创建输出目录
         * 
         * @param outputPath 输出路径
         * @return 是否成功
         */
        bool createOutputDirectory(const std::string& outputPath);

        /**
         * 获取图像尺寸
         * 
         * @param imagePath 图像路径
         * @param width 输出宽度
         * @param height 输出高度
         * @return 是否成功
         */
        bool getImageDimensions(const std::string& imagePath, int& width, int& height);

        // 私有成员变量
        // 简化实现，不使用PIMPL模式
    };

    // 工具函数

    /**
     * 解析矩形坐标字符串
     * 
     * @param coordinates 坐标字符串 "x1,y1,x2,y2"
     * @return 边界框
     */
    BoundingBox parseRectangleCoordinates(const std::string& coordinates);

    /**
     * 解析多边形坐标字符串
     * 
     * @param coordinates 坐标字符串 "x1,y1,x2,y2,x3,y3,..."
     * @return 边界框
     */
    BoundingBox parsePolygonCoordinates(const std::string& coordinates);

    /**
     * 计算多个边界框的联合边界框
     * 
     * @param boundingBoxes 边界框列表
     * @return 联合边界框
     */
    BoundingBox calculateUnionBoundingBox(const std::vector<BoundingBox>& boundingBoxes);

    /**
     * 限制边界框在图像范围内
     * 
     * @param bbox 边界框
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 限制后的边界框
     */
    BoundingBox clampBoundingBox(const BoundingBox& bbox, int imageWidth, int imageHeight);

} // namespace ImageCrop

#endif // IMAGE_CROP_H

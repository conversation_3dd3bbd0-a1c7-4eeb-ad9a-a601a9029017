package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationCategoriesService extends IService<AnnotationCategories>
{
    /**
     * 查询标注分类
     * 
     * @param categoryId 标注分类主键
     * @return 标注分类
     */
    AnnotationCategories selectAnnotationCategoriesByCategoryId(Long categoryId);

    /**
     * 查询标注分类列表
     * 
     * @param annotationCategories 标注分类
     * @return 标注分类集合
     */
    List<AnnotationCategories> selectAnnotationCategoriesList(AnnotationCategories annotationCategories);

    /**
     * 新增标注分类
     * 
     * @param annotationCategories 标注分类
     * @return 结果
     */
    int insertAnnotationCategories(AnnotationCategories annotationCategories);

    /**
     * 修改标注分类
     * 
     * @param annotationCategories 标注分类
     * @return 结果
     */
    int updateAnnotationCategories(AnnotationCategories annotationCategories);

    /**
     * 批量删除标注分类
     * 
     * @param categoryIds 需要删除的标注分类主键集合
     * @return 结果
     */
    int deleteAnnotationCategoriesByCategoryIds(Long[] categoryIds);

    /**
     * 删除标注分类信息
     * 
     * @param categoryId 标注分类主键
     * @return 结果
     */
    int deleteAnnotationCategoriesByCategoryId(Long categoryId);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationCategoriesImages" id="AnnotationCategoriesImagesResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="imageId"    column="image_id"    />
        <result property="annotatorId"    column="annotator_id"    />
        <result property="claimTime"    column="claim_time"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationCategoriesImagesVo">
        select id, category_id, image_id, annotator_id, claim_time, status, create_time, update_time, create_by, update_by from annotation_categories_images
    </sql>

    <select id="selectAnnotationCategoriesImagesList" parameterType="com.ylzx.annotation.domain.AnnotationCategoriesImages" resultMap="AnnotationCategoriesImagesResult">
        <include refid="selectAnnotationCategoriesImagesVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectAnnotationCategoriesImagesByCategoryId" parameterType="Long" resultMap="AnnotationCategoriesImagesResult">
        <include refid="selectAnnotationCategoriesImagesVo"/>
        where category_id = #{categoryId}
    </select>

    <insert id="insertAnnotationCategoriesImages" parameterType="com.ylzx.annotation.domain.AnnotationCategoriesImages">
        insert into annotation_categories_images
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="imageId != null">image_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="imageId != null">#{imageId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationCategoriesImages" parameterType="com.ylzx.annotation.domain.AnnotationCategoriesImages">
        update annotation_categories_images
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where category_id = #{categoryId} and image_id = #{imageId}
    </update>

    <delete id="deleteAnnotationCategoriesImagesByCategoryId" parameterType="Long">
        delete from annotation_categories_images where category_id = #{categoryId}
    </delete>

    <delete id="deleteAnnotationCategoriesImagesByCategoryIds" parameterType="String">
        delete from annotation_categories_images where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into annotation_categories_images (
            category_id, image_id, status, create_time, update_time, create_by, update_by
        ) 
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.categoryId}, #{item.imageId}, #{item.status}, 
                #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        update annotation_categories_images
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" item="item">
                    when (category_id = #{item.categoryId} and image_id = #{item.imageId}) then #{item.status}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" item="item">
                    when (category_id = #{item.categoryId} and image_id = #{item.imageId}) then #{item.updateTime}
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" item="item">
                    when (category_id = #{item.categoryId} and image_id = #{item.imageId}) then #{item.updateBy}
                </foreach>
            </trim>
        </trim>
        where (category_id, image_id) in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.categoryId}, #{item.imageId})
        </foreach>
    </update>

    <!--
      步骤1: 查询并锁定可用的图片ID
      - FOR UPDATE: 对查询的行加锁，防止其他事务操作。
      - SKIP LOCKED: (需要MySQL 8.0+或PostgreSQL等) 跳过已被其他事务锁定的行，避免等待，是实现高并发任务队列的关键。
    -->
    <select id="findAvailableImageIds" resultType="java.lang.Long">
        SELECT id FROM annotation_categories_images
        WHERE category_id = #{categoryId} AND status = #{status}
        ORDER BY id ASC
        LIMIT #{limit}
        FOR UPDATE SKIP LOCKED
    </select>

    <!--
      步骤2: 更新状态，将任务分配给用户
    -->
    <update id="assignImagesToUser">
        UPDATE annotation_categories_images
        SET
            annotator_id = #{userId},
            claim_time = #{claimTime},
            status = #{targetStatus}
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--
      步骤3: 根据ID列表查询完整的任务信息
    -->
    <select id="findByIds" resultMap="AnnotationCategoriesImagesResult">
        <include refid="selectAnnotationCategoriesImagesVo"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--
      步骤4: (定时任务用) 释放超时的任务
    -->
    <update id="releaseTimedOutImages">
        UPDATE annotation_categories_images
        SET
            annotator_id = NULL,
            claim_time = NULL,
            status = #{targetStatus}
        WHERE
            status = #{sourceStatus}
            AND claim_time &lt; #{timeoutThreshold}
    </update>

    <!--
      根据用户ID和状态查询任务
    -->
    <select id="findUserTasksByStatus" resultMap="AnnotationCategoriesImagesResult">
        <include refid="selectAnnotationCategoriesImagesVo"/>
        WHERE
            category_id = #{categoryId}
            AND annotator_id = #{userId}
            AND status = #{status}
    </select>

    <!--
      根据ID列表释放任务 (将状态改回未标注，并清除分配信息)
    -->
    <update id="releaseTasksByIds">
        UPDATE annotation_categories_images
        SET
            annotator_id = NULL,
            claim_time = NULL,
            status = #{targetStatus}
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--
      根据用户ID和多个状态查询任务列表，支持可选的categoryId
    -->
    <select id="findUserTasksByStatuses" resultMap="AnnotationCategoriesImagesResult">
        <include refid="selectAnnotationCategoriesImagesVo"/>
        WHERE
            annotator_id = #{userId}
            <if test="categoryId != null">
                AND category_id = #{categoryId}
            </if>
            AND status IN
            <foreach item="status" collection="statuses" open="(" separator="," close=")">
                #{status}
            </foreach>
    </select>

    <!--
      根据用户ID和多个状态查询所有分类下的任务列表
    -->
    <select id="findAllUserTasksByStatuses" resultMap="AnnotationCategoriesImagesResult">
        <include refid="selectAnnotationCategoriesImagesVo"/>
        WHERE
            annotator_id = #{userId}
            AND status IN
            <foreach item="status" collection="statuses" open="(" separator="," close=")">
                #{status}
            </foreach>
    </select>
</mapper>
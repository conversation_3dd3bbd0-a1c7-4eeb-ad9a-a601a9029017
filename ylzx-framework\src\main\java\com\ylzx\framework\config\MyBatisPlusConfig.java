package com.ylzx.framework.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.ylzx.common.core.domain.model.LoginUser;
import com.ylzx.common.utils.SecurityUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Mybatis Plus 配置
 *
 * <AUTHOR>
 */
@Configuration
public class MyBatisPlusConfig {

    /**
     * MybatisPlus拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        return interceptor;
    }

    /**
     * 元对象字段填充控制器
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
                this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());

                Optional.ofNullable(SecurityUtils.getLoginUser())
                        .map(LoginUser::getUsername)
                        .ifPresent(username -> {
                            this.strictInsertFill(metaObject, "createBy", String.class, username);
                            this.strictInsertFill(metaObject, "updateBy", String.class, username);
                        });
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());

                Optional.ofNullable(SecurityUtils.getLoginUser())
                        .map(LoginUser::getUsername)
                        .ifPresent(username -> this.strictUpdateFill(metaObject, "updateBy", String.class, username));
            }
        };
    }
} 
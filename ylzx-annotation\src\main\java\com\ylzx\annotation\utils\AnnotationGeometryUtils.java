package com.ylzx.annotation.utils;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.service.ImageCropService.BoundingBox;
import com.ylzx.common.core.utils.annotation.Point;
import com.ylzx.common.core.utils.annotation.Polygon;
import com.ylzx.common.core.utils.annotation.Rectangle;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 标注几何工具类
 * 处理标注坐标解析、几何计算等功能
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
public class AnnotationGeometryUtils {

    /**
     * 解析标注坐标为几何形状
     * 
     * @param annotation 标注对象
     * @return 几何形状对象
     */
    public static Object parseAnnotationShape(AnnotationAnnotations annotation) {
        String shapeType = annotation.getShapeType();
        String coordinates = annotation.getCoordinates();

        try {
            if ("0".equals(shapeType)) {
                // 矩形标注
                return parseRectangleCoordinates(coordinates);
            } else if ("1".equals(shapeType)) {
                // 多边形标注
                return parsePolygonCoordinates(coordinates);
            } else {
                log.warn("未知的形状类型: {}", shapeType);
                return null;
            }
        } catch (Exception e) {
            log.error("解析标注坐标失败: shapeType={}, coordinates={}", shapeType, coordinates, e);
            return null;
        }
    }

    /**
     * 解析矩形坐标
     * 支持多种格式：
     * - "x1,y1,x2,y2"
     * - "x1 y1, x2 y2"
     * - "x1,y1 x2,y2"
     */
    public static Rectangle parseRectangleCoordinates(String coordinates) {
        if (coordinates == null || coordinates.trim().isEmpty()) {
            throw new IllegalArgumentException("矩形坐标不能为空");
        }

        // 标准化坐标字符串，统一使用逗号分隔
        String normalized = coordinates.trim()
                .replaceAll("\\s+", " ")  // 多个空格替换为单个空格
                .replaceAll("\\s*,\\s*", ",")  // 逗号前后的空格去除
                .replaceAll("\\s+", ",");  // 剩余空格替换为逗号

        String[] parts = normalized.split(",");
        if (parts.length < 4) {
            throw new IllegalArgumentException("矩形坐标格式错误，需要至少4个数值: " + coordinates);
        }

        try {
            double x1 = Double.parseDouble(parts[0]);
            double y1 = Double.parseDouble(parts[1]);
            double x2 = Double.parseDouble(parts[2]);
            double y2 = Double.parseDouble(parts[3]);

            return new Rectangle(new Point(x1, y1), new Point(x2, y2));
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("矩形坐标包含无效数值: " + coordinates, e);
        }
    }

    /**
     * 解析多边形坐标
     * 支持多种格式：
     * - "x1,y1,x2,y2,x3,y3,..."
     * - "x1 y1, x2 y2, x3 y3, ..."
     * - "x1,y1 x2,y2 x3,y3 ..."
     */
    public static Polygon parsePolygonCoordinates(String coordinates) {
        if (coordinates == null || coordinates.trim().isEmpty()) {
            throw new IllegalArgumentException("多边形坐标不能为空");
        }

        // 标准化坐标字符串
        String normalized = coordinates.trim()
                .replaceAll("\\s+", " ")
                .replaceAll("\\s*,\\s*", ",")
                .replaceAll("\\s+", ",");

        String[] parts = normalized.split(",");
        if (parts.length < 6 || parts.length % 2 != 0) {
            throw new IllegalArgumentException("多边形坐标格式错误，需要至少3个点（6个数值）且为偶数个: " + coordinates);
        }

        List<Point> points = new ArrayList<>();
        try {
            for (int i = 0; i < parts.length; i += 2) {
                double x = Double.parseDouble(parts[i]);
                double y = Double.parseDouble(parts[i + 1]);
                points.add(new Point(x, y));
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("多边形坐标包含无效数值: " + coordinates, e);
        }

        return new Polygon(points);
    }

    /**
     * 计算多个标注的联合边界框
     * 
     * @param annotations 标注列表
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 联合边界框
     */
    public static BoundingBox calculateUnionBoundingBox(List<AnnotationAnnotations> annotations, 
                                                       int imageWidth, int imageHeight) {
        if (annotations == null || annotations.isEmpty()) {
            return null;
        }

        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        double maxX = Double.MIN_VALUE;
        double maxY = Double.MIN_VALUE;

        boolean hasValidAnnotation = false;

        for (AnnotationAnnotations annotation : annotations) {
            try {
                Object shape = parseAnnotationShape(annotation);
                if (shape == null) {
                    continue;
                }

                BoundingBox bounds = getShapeBoundingBox(shape);
                if (bounds != null) {
                    minX = Math.min(minX, bounds.getX());
                    minY = Math.min(minY, bounds.getY());
                    maxX = Math.max(maxX, bounds.getX() + bounds.getWidth());
                    maxY = Math.max(maxY, bounds.getY() + bounds.getHeight());
                    hasValidAnnotation = true;
                }
            } catch (Exception e) {
                log.warn("处理标注时出错: {}", annotation.getCoordinates(), e);
            }
        }

        if (!hasValidAnnotation) {
            return null;
        }

        // 确保边界在图像范围内
        int x = Math.max(0, (int) Math.floor(minX));
        int y = Math.max(0, (int) Math.floor(minY));
        int width = Math.min(imageWidth - x, (int) Math.ceil(maxX - minX));
        int height = Math.min(imageHeight - y, (int) Math.ceil(maxY - minY));

        return new BoundingBox(x, y, width, height);
    }

    /**
     * 获取几何形状的边界框
     */
    public static BoundingBox getShapeBoundingBox(Object shape) {
        if (shape instanceof Rectangle) {
            Rectangle rect = (Rectangle) shape;
            return new BoundingBox(
                (int) rect.getTopLeft().getX(),
                (int) rect.getTopLeft().getY(),
                (int) rect.getWidth(),
                (int) rect.getHeight()
            );
        } else if (shape instanceof Polygon) {
            Polygon polygon = (Polygon) shape;
            return calculatePolygonBoundingBox(polygon);
        }
        return null;
    }

    /**
     * 计算多边形的边界框
     */
    private static BoundingBox calculatePolygonBoundingBox(Polygon polygon) {
        List<Point> points = polygon.getPoints();
        if (points.isEmpty()) {
            return null;
        }

        double minX = points.get(0).getX();
        double minY = points.get(0).getY();
        double maxX = minX;
        double maxY = minY;

        for (Point point : points) {
            minX = Math.min(minX, point.getX());
            minY = Math.min(minY, point.getY());
            maxX = Math.max(maxX, point.getX());
            maxY = Math.max(maxY, point.getY());
        }

        return new BoundingBox(
            (int) Math.floor(minX),
            (int) Math.floor(minY),
            (int) Math.ceil(maxX - minX),
            (int) Math.ceil(maxY - minY)
        );
    }

    /**
     * 检查点是否在矩形内
     */
    public static boolean isPointInRectangle(Point point, Rectangle rectangle) {
        double x = point.getX();
        double y = point.getY();
        double left = rectangle.getTopLeft().getX();
        double top = rectangle.getTopLeft().getY();
        double right = rectangle.getBottomRight().getX();
        double bottom = rectangle.getBottomRight().getY();

        return x >= left && x <= right && y >= top && y <= bottom;
    }

    /**
     * 检查两个矩形是否相交
     */
    public static boolean doRectanglesIntersect(Rectangle rect1, Rectangle rect2) {
        double left1 = rect1.getTopLeft().getX();
        double top1 = rect1.getTopLeft().getY();
        double right1 = rect1.getBottomRight().getX();
        double bottom1 = rect1.getBottomRight().getY();

        double left2 = rect2.getTopLeft().getX();
        double top2 = rect2.getTopLeft().getY();
        double right2 = rect2.getBottomRight().getX();
        double bottom2 = rect2.getBottomRight().getY();

        return !(right1 < left2 || right2 < left1 || bottom1 < top2 || bottom2 < top1);
    }

    /**
     * 计算两个矩形的交集
     */
    public static Rectangle calculateRectangleIntersection(Rectangle rect1, Rectangle rect2) {
        if (!doRectanglesIntersect(rect1, rect2)) {
            return null;
        }

        double left = Math.max(rect1.getTopLeft().getX(), rect2.getTopLeft().getX());
        double top = Math.max(rect1.getTopLeft().getY(), rect2.getTopLeft().getY());
        double right = Math.min(rect1.getBottomRight().getX(), rect2.getBottomRight().getX());
        double bottom = Math.min(rect1.getBottomRight().getY(), rect2.getBottomRight().getY());

        return new Rectangle(new Point(left, top), new Point(right, bottom));
    }

    /**
     * 验证坐标是否在图像范围内
     */
    public static boolean isCoordinateValid(double x, double y, int imageWidth, int imageHeight) {
        return x >= 0 && x < imageWidth && y >= 0 && y < imageHeight;
    }

    /**
     * 将坐标限制在图像范围内
     */
    public static Point clampCoordinate(Point point, int imageWidth, int imageHeight) {
        double x = Math.max(0, Math.min(point.getX(), imageWidth - 1));
        double y = Math.max(0, Math.min(point.getY(), imageHeight - 1));
        return new Point(x, y);
    }
}

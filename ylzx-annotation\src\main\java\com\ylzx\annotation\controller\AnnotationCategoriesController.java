package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationCategoriesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 标注分类Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注分类管理")
@RestController
@RequestMapping("/annotation/categories")
public class AnnotationCategoriesController extends BaseController
{
    @Resource
    private AnnotationCategoriesService annotationCategoriesService;

    /**
     * 查询标注分类列表
     */
    @ApiOperation("查询标注分类列表")
    @PreAuthorize("@ss.hasPermi('system:categories:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnotationCategories annotationCategories)
    {
        startPage();
        List<AnnotationCategories> list = annotationCategoriesService.selectAnnotationCategoriesList(annotationCategories);
        return getDataTable(list);
    }

    /**
     * 导出标注分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:categories:export')")
    @Log(title = "标注分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationCategories annotationCategories)
    {
        List<AnnotationCategories> list = annotationCategoriesService.selectAnnotationCategoriesList(annotationCategories);
        ExcelUtil<AnnotationCategories> util = new ExcelUtil<AnnotationCategories>(AnnotationCategories.class);
        util.exportExcel(response, list, "标注分类数据");
    }

    /**
     * 获取标注分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:categories:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        return success(annotationCategoriesService.selectAnnotationCategoriesByCategoryId(categoryId));
    }

    /**
     * 新增标注分类
     */
    @PreAuthorize("@ss.hasPermi('system:categories:add')")
    @Log(title = "标注分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationCategories annotationCategories)
    {
        return toAjax(annotationCategoriesService.insertAnnotationCategories(annotationCategories));
    }

    /**
     * 修改标注分类
     */
    @PreAuthorize("@ss.hasPermi('system:categories:edit')")
    @Log(title = "标注分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationCategories annotationCategories)
    {
        return toAjax(annotationCategoriesService.updateAnnotationCategories(annotationCategories));
    }

    /**
     * 删除标注分类
     */
    @PreAuthorize("@ss.hasPermi('system:categories:remove')")
    @Log(title = "标注分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable("categoryIds") Long[] categoryIds)
    {
        return toAjax(annotationCategoriesService.deleteAnnotationCategoriesByCategoryIds(categoryIds));
    }
}

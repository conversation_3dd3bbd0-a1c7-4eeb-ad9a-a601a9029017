package com.ylzx.framework.config;

import com.alibaba.druid.filter.stat.StatFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 自定义Druid StatFilter，解决PostgreSQL语法解析问题
 */
@Configuration
public class CustomDruidStatFilter {
    
    @Bean
    @Primary
    public StatFilter statFilter() {
        StatFilter filter = new StatFilter();
        filter.setMergeSql(false); // 禁用SQL合并功能，避免PostgreSQL特殊语法的解析错误
        filter.setDbType("postgresql"); // 显式设置数据库类型为PostgreSQL
        return filter;
    }
} 
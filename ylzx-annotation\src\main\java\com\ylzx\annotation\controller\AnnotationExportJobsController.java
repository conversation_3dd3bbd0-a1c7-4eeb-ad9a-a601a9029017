package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationExportJobsService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationExportJobs;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 标注导出任务Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注导出任务管理")
@RestController
@RequestMapping("/annotation/jobs")
public class AnnotationExportJobsController extends BaseController
{
    @Resource
    private AnnotationExportJobsService annotationExportJobsService;

    /**
     * 查询标注导出任务列表
     */
    @ApiOperation("查询标注导出任务列表")
    @PreAuthorize("@ss.hasPermi('system:jobs:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnotationExportJobs annotationExportJobs)
    {
        startPage();
        List<AnnotationExportJobs> list = annotationExportJobsService.selectAnnotationExportJobsList(annotationExportJobs);
        return getDataTable(list);
    }

    /**
     * 导出标注导出任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:export')")
    @Log(title = "标注导出任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationExportJobs annotationExportJobs)
    {
        List<AnnotationExportJobs> list = annotationExportJobsService.selectAnnotationExportJobsList(annotationExportJobs);
        ExcelUtil<AnnotationExportJobs> util = new ExcelUtil<AnnotationExportJobs>(AnnotationExportJobs.class);
        util.exportExcel(response, list, "标注导出任务数据");
    }

    /**
     * 获取标注导出任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:query')")
    @GetMapping(value = "/{jobId}")
    public AjaxResult getInfo(@PathVariable("jobId") Long jobId)
    {
        return success(annotationExportJobsService.selectAnnotationExportJobsByJobId(jobId));
    }

    /**
     * 新增标注导出任务
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:add')")
    @Log(title = "标注导出任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationExportJobs annotationExportJobs)
    {
        return toAjax(annotationExportJobsService.insertAnnotationExportJobs(annotationExportJobs));
    }

    /**
     * 修改标注导出任务
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:edit')")
    @Log(title = "标注导出任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationExportJobs annotationExportJobs)
    {
        return toAjax(annotationExportJobsService.updateAnnotationExportJobs(annotationExportJobs));
    }

    /**
     * 删除标注导出任务
     */
    @PreAuthorize("@ss.hasPermi('system:jobs:remove')")
    @Log(title = "标注导出任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{jobIds}")
    public AjaxResult remove(@PathVariable("jobIds") Long[] jobIds)
    {
        return toAjax(annotationExportJobsService.deleteAnnotationExportJobsByJobIds(jobIds));
    }
}

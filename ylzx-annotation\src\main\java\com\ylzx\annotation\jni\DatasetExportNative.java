package com.ylzx.annotation.jni;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 数据集导出JNI接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Component
public class DatasetExportNative {

    private static boolean libraryLoaded = false;

    static {
        try {
            // 尝试从resources目录加载本地库
            loadLibraryFromResources();
            libraryLoaded = true;
            log.info("成功加载数据集导出本地库");
        } catch (Exception e) {
            log.warn("无法加载数据集导出本地库，将使用Java实现: {}", e.getMessage());
            libraryLoaded = false;
        }
    }

    /**
     * 从resources目录加载本地库
     */
    private static void loadLibraryFromResources() throws Exception {
        // 检测操作系统和架构
        String osName = System.getProperty("os.name").toLowerCase();
        String osArch = System.getProperty("os.arch").toLowerCase();

        String libraryName;
        String fileExtension;
        String tempFilePrefix = "dataset_export";

        // 根据操作系统确定库文件名和扩展名
        if (osName.contains("windows")) {
            libraryName = "dataset_export.dll";
            fileExtension = ".dll";
        } else if (osName.contains("linux")) {
            libraryName = "libdataset_export.so";
            fileExtension = ".so";
        } else if (osName.contains("mac") || osName.contains("darwin")) {
            libraryName = "libdataset_export.dylib";
            fileExtension = ".dylib";
        } else {
            throw new UnsupportedOperationException("不支持的操作系统: " + osName);
        }

        // 构建资源路径，支持架构特定的库文件
        String resourcePath = "/native/" + libraryName;
        String archSpecificPath = "/native/" + osArch + "/" + libraryName;

        // 优先尝试加载架构特定的库文件
        java.io.InputStream inputStream = DatasetExportNative.class.getResourceAsStream(archSpecificPath);
        String actualPath = archSpecificPath;

        // 如果架构特定的文件不存在，使用通用文件
        if (inputStream == null) {
            inputStream = DatasetExportNative.class.getResourceAsStream(resourcePath);
            actualPath = resourcePath;
        }

        if (inputStream == null) {
            throw new RuntimeException("找不到本地库文件: " + resourcePath + " 或 " + archSpecificPath);
        }

        log.info("正在加载本地库: {} (操作系统: {}, 架构: {})", actualPath, osName, osArch);

        try (java.io.InputStream stream = inputStream) {
            // 创建临时文件
            java.nio.file.Path tempFile = java.nio.file.Files.createTempFile(tempFilePrefix, fileExtension);
            tempFile.toFile().deleteOnExit();

            // 复制库文件到临时文件
            java.nio.file.Files.copy(stream, tempFile, java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            // 在Linux/macOS上设置执行权限
            if (!osName.contains("windows")) {
                tempFile.toFile().setExecutable(true);
                tempFile.toFile().setReadable(true);
            }

            // 加载临时文件
            System.load(tempFile.toAbsolutePath().toString());

            log.info("成功从临时文件加载本地库: {}", tempFile.toAbsolutePath());
        }
    }

    /**
     * 检查本地库是否已加载
     * 
     * @return 是否已加载
     */
    public static boolean isLibraryLoaded() {
        return libraryLoaded;
    }

    /**
     * 导出数据集配置
     */
    public static class ExportConfig {
        public String exportFormat;      // 导出格式: coco, voc, yolo
        public String outputPath;        // 输出路径
        public int targetWidth;          // 目标宽度
        public int targetHeight;         // 目标高度
        public String transformations;   // 变换配置JSON
        public boolean grayscale;        // 是否转灰度
        public boolean enableMask;       // 是否启用mask
        public double rotationAngle;     // 旋转角度
        public double scaleRatio;        // 缩放比例
    }

    /**
     * 图像信息
     */
    public static class ImageInfo {
        public long imageId;
        public String imagePath;
        public String annotationData;    // 标注数据JSON
        public String datasetType;       // train, validation, test
        public int originalWidth;
        public int originalHeight;
    }

    /**
     * 导出结果
     */
    public static class ExportResult {
        public boolean success;
        public String message;
        public int processedCount;
        public String outputPath;
        public long processingTimeMs;     // 处理耗时（毫秒）
        public String[] failedImages;     // 处理失败的图像路径
    }

    /**
     * 批量图像变换配置
     */
    public static class BatchTransformConfig {
        public String[] sourcePaths;     // 源图像路径数组
        public String[] targetPaths;     // 目标图像路径数组
        public int targetWidth;          // 目标宽度
        public int targetHeight;         // 目标高度
        public boolean grayscale;        // 是否转灰度
        public boolean enableMask;       // 是否启用mask
        public double rotationAngle;     // 旋转角度
        public double scaleRatio;        // 缩放比例
        public String transformations;   // 额外变换配置JSON
        public int threadCount;          // 并行处理线程数
    }

    /**
     * 批量处理结果
     */
    public static class BatchProcessResult {
        public boolean success;
        public String message;
        public int totalCount;           // 总数量
        public int successCount;         // 成功数量
        public int failedCount;          // 失败数量
        public long processingTimeMs;    // 处理耗时（毫秒）
        public String[] failedPaths;     // 失败的图像路径
        public String[] errorMessages;   // 对应的错误信息
    }

    /**
     * 导出COCO格式数据集
     * 
     * @param config 导出配置
     * @param images 图像信息数组
     * @param categories 分类信息JSON
     * @return 导出结果
     */
    public native ExportResult exportCOCO(ExportConfig config, ImageInfo[] images, String categories);

    /**
     * 导出VOC格式数据集
     * 
     * @param config 导出配置
     * @param images 图像信息数组
     * @param categories 分类信息JSON
     * @return 导出结果
     */
    public native ExportResult exportVOC(ExportConfig config, ImageInfo[] images, String categories);

    /**
     * 导出YOLO格式数据集
     * 
     * @param config 导出配置
     * @param images 图像信息数组
     * @param categories 分类信息JSON
     * @return 导出结果
     */
    public native ExportResult exportYOLO(ExportConfig config, ImageInfo[] images, String categories);

    /**
     * 应用图像变换（单张图像）
     *
     * @param imagePath 图像路径
     * @param outputPath 输出路径
     * @param transformations 变换配置JSON
     * @return 是否成功
     */
    public native boolean applyImageTransformations(String imagePath, String outputPath, String transformations);

    /**
     * 批量应用图像变换（推荐使用）
     *
     * @param config 批量变换配置
     * @return 批量处理结果
     */
    public native BatchProcessResult batchApplyImageTransformations(BatchTransformConfig config);

    /**
     * 批量复制和变换图像到数据集文件夹
     *
     * @param config 导出配置
     * @param images 图像信息数组
     * @param outputBasePath 输出基础路径
     * @return 批量处理结果
     */
    public native BatchProcessResult batchProcessImagesToDataset(ExportConfig config, ImageInfo[] images, String outputBasePath);

    /**
     * 验证导出配置
     * 
     * @param config 导出配置
     * @return 验证结果消息
     */
    public native String validateConfig(ExportConfig config);

    /**
     * 获取支持的导出格式列表
     * 
     * @return 支持的格式数组
     */
    public native String[] getSupportedFormats();

    /**
     * 获取本地库版本信息
     * 
     * @return 版本信息
     */
    public native String getLibraryVersion();

    /**
     * 清理临时文件
     *
     * @param tempDir 临时目录路径
     * @return 是否成功
     */
    public native boolean cleanupTempFiles(String tempDir);

    // ==================== 图像裁剪相关方法 ====================

    /**
     * 图像裁剪配置
     */
    public static class CropConfig {
        public int targetWidth;
        public int targetHeight;
        public int padding;
        public boolean enableRandomPlacement;
        public int[] backgroundColor;  // RGB值
        public boolean maintainAspectRatio;
    }

    /**
     * 裁剪区域
     */
    public static class CropRegion {
        public int x;
        public int y;
        public int width;
        public int height;
    }

    /**
     * 批量裁剪配置
     */
    public static class BatchCropConfig {
        public String[] sourcePaths;      // 源图像路径数组
        public String[] targetPaths;      // 目标图像路径数组
        public CropRegion[] cropRegions;  // 裁剪区域数组
        public CropConfig cropConfig;     // 裁剪配置
        public int threadCount;           // 并行处理线程数
    }

    /**
     * 批量裁剪结果
     */
    public static class BatchCropResult {
        public boolean success;
        public String message;
        public int totalCount;
        public int successCount;
        public int failedCount;
        public long processingTimeMs;
        public String[] outputPaths;     // 成功处理的输出路径
        public String[] failedPaths;     // 失败的图像路径
        public String[] errorMessages;   // 对应的错误信息
    }

    /**
     * 单张图像裁剪
     *
     * @param imagePath 源图像路径
     * @param outputPath 输出路径
     * @param cropRegion 裁剪区域
     * @param cropConfig 裁剪配置
     * @return 是否成功
     */
    public native boolean cropImage(String imagePath, String outputPath, CropRegion cropRegion, CropConfig cropConfig);

    /**
     * 批量图像裁剪（推荐使用）
     *
     * @param config 批量裁剪配置
     * @return 批量裁剪结果
     */
    public native BatchCropResult batchCropImages(BatchCropConfig config);

    /**
     * 智能裁剪：根据标注坐标自动计算裁剪区域
     *
     * @param imagePath 源图像路径
     * @param outputPath 输出路径
     * @param annotationCoordinates 标注坐标JSON字符串
     * @param cropConfig 裁剪配置
     * @return 是否成功
     */
    public native boolean smartCropByAnnotations(String imagePath, String outputPath,
                                               String annotationCoordinates, CropConfig cropConfig);

    /**
     * 批量智能裁剪
     *
     * @param imagePaths 源图像路径数组
     * @param outputPaths 输出路径数组
     * @param annotationCoordinatesArray 标注坐标JSON字符串数组
     * @param cropConfig 裁剪配置
     * @param threadCount 并行线程数
     * @return 批量裁剪结果
     */
    public native BatchCropResult batchSmartCropByAnnotations(String[] imagePaths, String[] outputPaths,
                                                            String[] annotationCoordinatesArray,
                                                            CropConfig cropConfig, int threadCount);
}

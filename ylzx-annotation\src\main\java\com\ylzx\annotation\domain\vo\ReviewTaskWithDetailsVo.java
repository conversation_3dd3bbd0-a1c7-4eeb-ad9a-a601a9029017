package com.ylzx.annotation.domain.vo;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.domain.AnnotationReviews;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 审核任务详细信息VO
 * 用于返回审核任务信息、图片信息和对应的标注列表
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "审核任务详细信息")
public class ReviewTaskWithDetailsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 审核任务信息 */
    @Schema(description = "审核任务信息")
    private AnnotationReviews reviewTask;

    /** 图片信息 */
    @Schema(description = "图片信息")
    private AnnotationImages image;

    /** 该图片对应的标注列表 */
    @Schema(description = "标注列表")
    private List<AnnotationAnnotations> annotations;

    /** 当前审核的标注信息（主要标注） */
    @Schema(description = "当前审核的标注信息")
    private AnnotationAnnotations currentAnnotation;

    /** 审核员ID */
    @Schema(description = "审核员ID")
    private String auditor;

    /** 审核状态 */
    @Schema(description = "审核状态")
    private String reviewStatus;
}

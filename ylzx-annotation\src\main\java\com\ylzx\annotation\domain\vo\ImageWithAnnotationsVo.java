package com.ylzx.annotation.domain.vo;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 图片与标注组合VO
 * 用于返回图片信息和对应的标注列表
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "图片与标注组合信息")
public class ImageWithAnnotationsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 图片信息 */
    @Schema(description = "图片信息")
    private AnnotationImages image;

    /** 该图片对应的标注列表 */
    @Schema(description = "标注列表")
    private List<AnnotationAnnotations> annotations;

    /** 任务信息（可选，用于领取任务时返回） */
    @Schema(description = "任务信息")
    private Long taskId;

    /** 标注员ID（可选，用于领取任务时返回） */
    @Schema(description = "标注员ID")
    private Long annotatorId;

    /** 任务状态（可选，用于领取任务时返回） */
    @Schema(description = "任务状态")
    private String taskStatus;
}

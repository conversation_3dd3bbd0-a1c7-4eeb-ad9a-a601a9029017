package com.ylzx.annotation.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ylzx.annotation.mapper.AnnotationCategoriesSourcesMapper;
import com.ylzx.annotation.domain.AnnotationCategoriesSources;
import com.ylzx.annotation.service.AnnotationCategoriesSourcesService;
import lombok.extern.slf4j.Slf4j;

/**
 * 标注分类数据源关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Slf4j
@Service
public class AnnotationCategoriesSourcesServiceImpl extends ServiceImpl<AnnotationCategoriesSourcesMapper, AnnotationCategoriesSources> implements AnnotationCategoriesSourcesService
{
    @Autowired
    private AnnotationCategoriesSourcesMapper annotationCategoriesSourcesMapper;

    @Override
    public List<AnnotationCategoriesSources> selectAnnotationCategoriesSourcesList(AnnotationCategoriesSources annotationCategoriesSources)
    {
        return annotationCategoriesSourcesMapper.selectAnnotationCategoriesSourcesList(annotationCategoriesSources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationCategoriesSources associateCategoryWithSource(Long categoryId, Long sourceId, Long userId)
    {
        // 检查是否已经关联
        if (isAssociated(categoryId, sourceId)) {
            log.warn("分类{}和数据源{}已经关联，跳过创建", categoryId, sourceId);
            return null;
        }

        AnnotationCategoriesSources association = new AnnotationCategoriesSources();
        association.setCategoryId(categoryId);
        association.setSourceId(sourceId);
        association.setAssociatedAt(Date.from(LocalDateTime.now().toInstant(ZoneOffset.of("+8"))));
        association.setAssociatedByUserId(userId);
        association.setStatus("active");
        association.setCreateTime(LocalDateTime.now());

        this.save(association);
        log.info("成功创建分类{}和数据源{}的关联，关联ID: {}", categoryId, sourceId, association.getId());
        return association;
    }

    @Override
    public boolean isAssociated(Long categoryId, Long sourceId)
    {
        int count = annotationCategoriesSourcesMapper.countByCategoryIdAndSourceId(categoryId, sourceId);
        return count > 0;
    }

    @Override
    public List<Long> getSourceIdsByCategoryId(Long categoryId)
    {
        return annotationCategoriesSourcesMapper.selectSourceIdsByCategoryId(categoryId);
    }

    @Override
    public List<Long> getCategoryIdsBySourceId(Long sourceId)
    {
        return annotationCategoriesSourcesMapper.selectCategoryIdsBySourceId(sourceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchAssociateCategoryWithSources(Long categoryId, List<Long> sourceIds, Long userId)
    {
        if (sourceIds == null || sourceIds.isEmpty()) {
            return 0;
        }

        List<AnnotationCategoriesSources> associations = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (Long sourceId : sourceIds) {
            // 跳过已经关联的
            if (isAssociated(categoryId, sourceId)) {
                log.debug("分类{}和数据源{}已经关联，跳过", categoryId, sourceId);
                continue;
            }

            AnnotationCategoriesSources association = new AnnotationCategoriesSources();
            association.setCategoryId(categoryId);
            association.setSourceId(sourceId);
            association.setAssociatedAt(Date.from(now.toInstant(ZoneOffset.of("+8"))));
            association.setAssociatedByUserId(userId);
            association.setStatus("active");
            association.setCreateTime(now);
            associations.add(association);
        }

        if (!associations.isEmpty()) {
            int result = annotationCategoriesSourcesMapper.insertBatch(associations);
            log.info("批量创建分类{}和{}个数据源的关联", categoryId, result);
            return result;
        }

        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAllSourcesFromCategory(Long categoryId)
    {
        int result = annotationCategoriesSourcesMapper.deleteByCategoryId(categoryId);
        log.info("删除分类{}的所有数据源关联，删除{}条记录", categoryId, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAllCategoriesFromSource(Long sourceId)
    {
        int result = annotationCategoriesSourcesMapper.deleteBySourceId(sourceId);
        log.info("删除数据源{}的所有分类关联，删除{}条记录", sourceId, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAssociation(Long categoryId, Long sourceId)
    {
        QueryWrapper<AnnotationCategoriesSources> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category_id", categoryId)
                   .eq("source_id", sourceId);
        
        boolean result = this.remove(queryWrapper);
        if (result) {
            log.info("成功移除分类{}和数据源{}的关联", categoryId, sourceId);
        } else {
            log.warn("移除分类{}和数据源{}的关联失败，可能不存在该关联", categoryId, sourceId);
        }
        return result;
    }
}

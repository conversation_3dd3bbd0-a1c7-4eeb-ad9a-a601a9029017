-- sys_deploy_form definition
DROP TABLE IF EXISTS sys_deploy_form;
CREATE TABLE sys_deploy_form (
    id        SERIAL PRIMARY KEY,
    form_id   BIGINT DEFAULT NULL,
    deploy_id VARCHAR(50) DEFAULT NULL
);
COMMENT ON TABLE sys_deploy_form IS '流程实例关联表单';
COMMENT ON COLUMN sys_deploy_form.id IS '主键';
COMMENT ON COLUMN sys_deploy_form.form_id IS '表单主键';
COMMENT ON COLUMN sys_deploy_form.deploy_id IS '流程实例主键';

-- sys_expression definition
DROP TABLE IF EXISTS sys_expression;
CREATE TABLE sys_expression (
    id          SERIAL PRIMARY KEY,
    name        VARCHAR(50)  DEFAULT NULL,
    expression  VARCHAR(255) DEFAULT NULL,
    data_type   VARCHAR(255) DEFAULT NULL,
    create_time TIMESTAMP    DEFAULT NULL,
    update_time TIMESTAMP    DEFAULT NULL,
    create_by   BIGINT       DEFAULT NULL,
    update_by   BIGINT       DEFAULT NULL,
    status      SMALLINT     DEFAULT 0,
    remark      VARCHAR(255) DEFAULT NULL
);
COMMENT ON TABLE sys_expression IS '流程表达式';
COMMENT ON COLUMN sys_expression.id IS '表单主键';
COMMENT ON COLUMN sys_expression.name IS '表达式名称';
COMMENT ON COLUMN sys_expression.expression IS '表达式内容';
COMMENT ON COLUMN sys_expression.data_type IS '表达式类型';
COMMENT ON COLUMN sys_expression.create_time IS '创建时间';
COMMENT ON COLUMN sys_expression.update_time IS '更新时间';
COMMENT ON COLUMN sys_expression.create_by IS '创建人员';
COMMENT ON COLUMN sys_expression.update_by IS '更新人员';
COMMENT ON COLUMN sys_expression.status IS '状态';
COMMENT ON COLUMN sys_expression.remark IS '备注';

-- sys_form definition
DROP TABLE IF EXISTS sys_form;
CREATE TABLE sys_form (
    form_id      SERIAL PRIMARY KEY,
    form_name    VARCHAR(50)  DEFAULT NULL,
    form_content TEXT,
    create_time  TIMESTAMP    DEFAULT NULL,
    update_time  TIMESTAMP    DEFAULT NULL,
    create_by    BIGINT       DEFAULT NULL,
    update_by    BIGINT       DEFAULT NULL,
    remark       VARCHAR(255) DEFAULT NULL
);
COMMENT ON TABLE sys_form IS '流程表单';
COMMENT ON COLUMN sys_form.form_id IS '表单主键';
COMMENT ON COLUMN sys_form.form_name IS '表单名称';
COMMENT ON COLUMN sys_form.form_content IS '表单内容';
COMMENT ON COLUMN sys_form.create_time IS '创建时间';
COMMENT ON COLUMN sys_form.update_time IS '更新时间';
COMMENT ON COLUMN sys_form.create_by IS '创建人员';
COMMENT ON COLUMN sys_form.update_by IS '更新人员';
COMMENT ON COLUMN sys_form.remark IS '备注';

-- sys_listener definition
DROP TABLE IF EXISTS sys_listener;
CREATE TABLE sys_listener (
    id          SERIAL PRIMARY KEY,
    name        VARCHAR(128) DEFAULT NULL,
    type        CHAR(2)      DEFAULT NULL,
    event_type  VARCHAR(32)  DEFAULT NULL,
    value_type  VARCHAR(32)  DEFAULT NULL,
    value       VARCHAR(255) DEFAULT NULL,
    create_time TIMESTAMP    DEFAULT NULL,
    update_time TIMESTAMP    DEFAULT NULL,
    create_by   BIGINT       DEFAULT NULL,
    update_by   BIGINT       DEFAULT NULL,
    status      SMALLINT     DEFAULT 0,
    remark      VARCHAR(255) DEFAULT NULL
);
COMMENT ON TABLE sys_listener IS '流程监听';
COMMENT ON COLUMN sys_listener.id IS '表单主键';
COMMENT ON COLUMN sys_listener.name IS '名称';
COMMENT ON COLUMN sys_listener.type IS '监听类型';
COMMENT ON COLUMN sys_listener.event_type IS '事件类型';
COMMENT ON COLUMN sys_listener.value_type IS '值类型';
COMMENT ON COLUMN sys_listener.value IS '执行内容';
COMMENT ON COLUMN sys_listener.create_time IS '创建时间';
COMMENT ON COLUMN sys_listener.update_time IS '更新时间';
COMMENT ON COLUMN sys_listener.create_by IS '创建人员';
COMMENT ON COLUMN sys_listener.update_by IS '更新人员';
COMMENT ON COLUMN sys_listener.status IS '状态';
COMMENT ON COLUMN sys_listener.remark IS '备注';

-- 流程相关菜单
-- 注意: 菜单表应该在前面的ry_20240629_pg.sql中已创建，这里只插入数据
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2020, '流程管理', 0, 6, 'flowable', NULL, NULL, NULL, 1, 0, 'M', '0', '0', '', 'cascader', 'tony', NOW(), 'admin', '2022-12-29 17:39:22', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2022, '流程定义', 2020, 2, 'definition', 'flowable/definition/index', NULL, NULL, 1, 0, 'C', '0', '0', '', 'job', 'tony', NOW(), 'admin', '2022-12-29 17:40:39', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2023, '任务管理', 0, 7, 'task', NULL, NULL, NULL, 1, 0, 'M', '0', '0', '', 'dict', 'tony', NOW(), 'admin', '2021-03-29 09:37:40', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2024, '待办任务', 2023, 2, 'todo', 'flowable/task/todo/index', NULL, NULL, 1, 1, 'C', '0', '0', '', 'cascader', 'admin', NOW(), 'admin', '2021-03-30 09:26:36', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2025, '已办任务', 2023, 3, 'finished', 'flowable/task/finished/index', NULL, NULL, 1, 1, 'C', '0', '0', '', 'time-range', 'admin', NOW(), 'admin', '2021-03-30 09:26:50', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2026, '已发任务', 2023, 1, 'process', 'flowable/task/myProcess/index', NULL, NULL, 1, 1, 'C', '0', '0', '', 'guide', 'admin', NOW(), 'admin', '2022-12-12 09:58:07', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2027, '表单配置', 2020, 2, 'form', 'flowable/task/form/index', NULL, NULL, 1, 1, 'C', '0', '0', 'flowable:form:list', 'form', 'admin', NOW(), 'admin', '2023-08-19 15:54:57', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2028, '新增', 2027, 1, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'flowable:form:add', '#', 'admin', NOW(), 'admin', '2023-08-16 09:17:38', '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2029, '删除', 2027, 3, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'flowable:form:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2030, '编辑', 2027, 2, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'flowable:form:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2031, '新增', 2026, 1, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'system:deployment:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2032, '编辑', 2026, 2, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'system:deployment:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2033, '删除', 2026, 3, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'system:deployment:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2034, '查询', 2027, 4, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'flowable:form:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2035, '修改密码', 100, 8, '', NULL, NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:updatePwd', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2036, '流程表达式', 2020, 3, 'expression', 'flowable/expression/index', NULL, NULL, 1, 1, 'C', '0', '0', 'system:expression:list', 'list', 'admin', NOW(), 'admin', '2022-12-12 17:13:44', '流程达式菜单');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2037, '流程达式查询', 2036, 1, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:expression:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2038, '流程达式新增', 2036, 2, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:expression:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2039, '流程达式修改', 2036, 3, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:expression:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2040, '流程达式删除', 2036, 4, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:expression:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2041, '流程达式导出', 2036, 5, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:expression:export', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2042, '流程监听', 2020, 4, 'listener', 'flowable/listener/index', NULL, NULL, 1, 0, 'C', '0', '0', 'system:listener:list', 'monitor', 'admin', NOW(), 'admin', '2022-12-29 08:59:21', '流程监听菜单');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2043, '流程监听查询', 2042, 1, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:listener:query', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2044, '流程监听新增', 2042, 2, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:listener:add', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2045, '流程监听修改', 2042, 3, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:listener:edit', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2046, '流程监听删除', 2042, 4, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:listener:remove', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, route_name, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES(2047, '流程监听导出', 2042, 5, '#', '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:listener:export', '#', 'admin', NOW(), '', NULL, '');

-- 流程相关字段表信息
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(100, '表达式类型', 'exp_data_type', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(102, '监听类型', 'sys_listener_type', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(103, '监听值类型', 'sys_listener_value_type', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(104, '监听属性', 'sys_listener_event_type', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark) 
VALUES(105, '流程分类', 'sys_process_category', '0', 'admin', NOW(), '', NULL, NULL);


INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(100, 0, '系统指定', 'fixed', 'exp_data_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(101, 0, '动态选择', 'dynamic', 'exp_data_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(104, 0, '任务监听', '1', 'sys_listener_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(105, 2, '执行监听', '2', 'sys_listener_type', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(106, 0, 'JAVA类', 'classListener', 'sys_listener_value_type', NULL, 'default', 'N', '0', 'admin', NOW(), 'admin', '2024-09-05 21:38:02', NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(107, 0, '表达式', 'expressionListener', 'sys_listener_value_type', NULL, 'default', 'N', '0', 'admin', NOW(), 'admin', '2024-09-05 21:38:10', NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(108, 0, '代理表达式', 'delegateExpressionListener', 'sys_listener_value_type', NULL, 'default', 'N', '0', 'admin', NOW(), 'admin', '2024-09-05 21:38:16', NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(109, 0, '请假', 'leave', 'sys_process_category', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL);

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES(110, 0, '报销', 'expense', 'sys_process_category', NULL, 'default', 'N', '0', 'admin', NOW(), '', NULL, NULL); 
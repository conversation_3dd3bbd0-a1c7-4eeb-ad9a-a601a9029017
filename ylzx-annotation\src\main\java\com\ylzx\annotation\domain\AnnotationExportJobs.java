package com.ylzx.annotation.domain;

import java.io.Serial;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注导出任务对象 annotation_export_jobs
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationExportJobs extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注导出任务主键 */
    private Long jobId;

    /** 标注项目主键 */
    @Excel(name = "标注项目主键")
    private Long projectId;

    /** 导出格式 */
    @Excel(name = "导出格式")
    private String exportFormat;

    /** 标注状态 */
    @Excel(name = "标注状态", readConverterExp = "0=未标注,1=未审核,2=审核通过,3=审核不通过")
    @EnumValue
   private AnnotationStatus status;

    /** 输出路径 */
    @Excel(name = "输出路径")
    private String outputPath;

    /** 标注日志 */
    @Excel(name = "标注日志")
    private String jobLog;

    /** 完成时间 */
    @Excel(name = "完成时间")
    private String completedAt;
}

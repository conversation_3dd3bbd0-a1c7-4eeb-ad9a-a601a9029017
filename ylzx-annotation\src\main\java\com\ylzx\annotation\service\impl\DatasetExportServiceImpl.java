package com.ylzx.annotation.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.util.Comparator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.scheduling.annotation.Async;


import com.ylzx.annotation.service.DatasetExportService;
import com.ylzx.annotation.service.AnnotationExportJobsService;
import com.ylzx.annotation.service.AnnotationProjectsService;
import com.ylzx.annotation.service.AnnotationProjectCategoriesService;
import com.ylzx.annotation.service.AnnotationImageSourcesService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.domain.AnnotationExportJobs;
import com.ylzx.annotation.domain.AnnotationProjects;

import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.jni.DatasetExportNative;
import com.ylzx.annotation.utils.DatasetSplitUtils;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据集导出服务实现
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Service
public class DatasetExportServiceImpl implements DatasetExportService
{
    @Autowired
    private AnnotationExportJobsService exportJobsService;

    @Autowired
    private AnnotationProjectsService projectsService;
    
    @Autowired
    private AnnotationProjectCategoriesService projectCategoriesService;
    
    @Autowired
    private AnnotationImageSourcesService imageSourcesService;

    @Autowired
    private AnnotationImagesService imagesService;

    @Autowired
    private DatasetExportNative exportNative;
    
    // 任务进度缓存
    private final Map<Long, Integer> taskProgressCache = new ConcurrentHashMap<>();
    
    // 任务取消标志
    private final Set<Long> cancelledTasks = ConcurrentHashMap.newKeySet();

    /**
     * 验证导出配置
     */
    @Override
    public ExportValidationResult validateExportConfig(AnnotationProjects config, List<Long> categoryIds)
    {
        List<String> errors = new ArrayList<>();
        
        // 验证基本配置
        if (config == null) {
            return new ExportValidationResult(false, "项目配置不能为空");
        }
        
        if (StringUtils.isEmpty(config.getExportFormat())) {
            errors.add("导出格式不能为空");
        } else {
            List<String> supportedFormats = getSupportedExportFormats();
            if (!supportedFormats.contains(config.getExportFormat().toLowerCase())) {
                errors.add("不支持的导出格式: " + config.getExportFormat());
            }
        }
        
        // 验证数据集分割比例
        if (config.getTrainRatio() == null || config.getValidationRatio() == null || config.getTestRatio() == null) {
            errors.add("数据集分割比例不能为空");
        } else {
            BigDecimal totalRatio = config.getTrainRatio()
                .add(config.getValidationRatio())
                .add(config.getTestRatio());
            
            if (totalRatio.compareTo(BigDecimal.ONE) != 0) {
                errors.add("数据集分割比例之和必须等于1.0");
            }
            
            if (config.getTrainRatio().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("训练集比例必须大于0");
            }
        }
        
        // 验证导出路径
        if (StringUtils.isEmpty(config.getExportPath())) {
            errors.add("导出路径不能为空");
        } else {
            try {
                Path exportPath = Paths.get(config.getExportPath());
                if (!Files.exists(exportPath.getParent())) {
                    errors.add("导出路径的父目录不存在: " + exportPath.getParent());
                }
            } catch (Exception e) {
                errors.add("导出路径格式无效: " + config.getExportPath());
            }
        }
        
        // 验证分类ID
        if (categoryIds != null && !categoryIds.isEmpty()) {
            // 这里可以添加分类ID存在性验证
            for (Long categoryId : categoryIds) {
                if (categoryId == null || categoryId <= 0) {
                    errors.add("无效的分类ID: " + categoryId);
                }
            }
        }
        
        if (errors.isEmpty()) {
            return new ExportValidationResult(true, "配置验证通过");
        } else {
            return new ExportValidationResult(false, "配置验证失败", errors);
        }
    }

    /**
     * 创建导出任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createExportTask(Long projectId, List<Long> categoryIds, boolean incrementalExport)
    {
        try {
            // 获取项目配置
            AnnotationProjects config = projectsService.selectProjectWithCategoriesByProjectId(projectId);
            if (config == null) {
                throw new IllegalArgumentException("项目配置不存在: " + projectId);
            }
            
            // 验证配置
            ExportValidationResult validation = validateExportConfig(config, categoryIds);
            if (!validation.isValid()) {
                throw new IllegalArgumentException("配置验证失败: " + validation.getMessage());
            }
            
            // 创建导出任务记录
            AnnotationExportJobs job = new AnnotationExportJobs();
            job.setProjectId(projectId);
            job.setExportFormat(config.getExportFormat());
            job.setStatus(AnnotationStatus.NOT_ANNOTATED); // 使用未标注状态表示等待中
            job.setOutputPath(config.getExportPath());
            job.setJobLog("任务已创建，等待执行");
            
            // 保存任务
            exportJobsService.insertAnnotationExportJobs(job);
            
            log.info("创建导出任务成功，任务ID: {}, 项目ID: {}, 格式: {}, 增量导出: {}", 
                    job.getJobId(), projectId, config.getExportFormat(), incrementalExport);
            
            return job.getJobId();
            
        } catch (Exception e) {
            log.error("创建导出任务失败，项目ID: {}", projectId, e);
            throw e;
        }
    }

    /**
     * 异步执行导出任务
     */
    @Override
    @Async
    public CompletableFuture<Boolean> executeExportTaskAsync(Long jobId)
    {
        return CompletableFuture.supplyAsync(() -> executeExportTask(jobId));
    }

    /**
     * 同步执行导出任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeExportTask(Long jobId)
    {
        AnnotationExportJobs job = null;
        try {
            // 获取任务详情
            job = exportJobsService.selectAnnotationExportJobsByJobId(jobId);
            if (job == null) {
                log.error("导出任务不存在: {}", jobId);
                return false;
            }
            
            // 检查任务是否被取消
            if (cancelledTasks.contains(jobId)) {
                updateJobStatus(job, AnnotationStatus.REJECTED, "任务已被取消"); // 使用审核不通过状态表示取消
                return false;
            }
            
            // 更新任务状态为执行中
            updateJobStatus(job, AnnotationStatus.IN_PROGRESS, "开始执行导出任务");
            taskProgressCache.put(jobId, 0);
            
            // 获取项目配置
            AnnotationProjects config = projectsService.selectProjectWithCategoriesByProjectId(job.getProjectId());
            if (config == null) {
                updateJobStatus(job, AnnotationStatus.REJECTED, "项目配置不存在");
                return false;
            }
            
            // 执行导出逻辑
            boolean success = performExport(job, config);
            
            if (success) {
                updateJobStatus(job, AnnotationStatus.APPROVED, "导出任务完成");
                taskProgressCache.put(jobId, 100);
            } else {
                updateJobStatus(job, AnnotationStatus.REJECTED, "导出任务执行失败");
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("执行导出任务失败，任务ID: {}", jobId, e);
            if (job != null) {
                updateJobStatus(job, AnnotationStatus.REJECTED, "导出任务异常: " + e.getMessage());
            }
            return false;
        } finally {
            // 清理资源
            cancelledTasks.remove(jobId);
            // 保留进度信息一段时间，便于查询
        }
    }

    /**
     * 执行具体的导出逻辑
     */
    private boolean performExport(AnnotationExportJobs job, AnnotationProjects config)
    {
        try {
            Long projectId = job.getProjectId();
            String projectDatasetPath = getProjectDatasetPath(projectId);

            // 1. 检查是否需要全量重新生成
            updateJobProgress(job.getJobId(), 10, "检查导出策略...");
            boolean needFullRegeneration = checkNeedFullRegeneration(projectId, config, projectDatasetPath);

            if (needFullRegeneration) {
                updateJobProgress(job.getJobId(), 15, "清理旧数据集文件夹...");
                clearProjectDatasetFolder(projectDatasetPath);
                updateJobProgress(job.getJobId(), 20, "执行全量生成...");
                return performFullGeneration(job, config, projectDatasetPath);
            } else {
                updateJobProgress(job.getJobId(), 20, "执行增量更新...");
                return performIncrementalUpdate(job, config, projectDatasetPath);
            }

        } catch (Exception e) {
            log.error("执行导出逻辑失败，任务ID: {}", job.getJobId(), e);
            return false;
        }
    }

    /**
     * 获取项目数据集存储路径
     */
    private String getProjectDatasetPath(Long projectId)
    {
        // 基于annotation_data目录创建项目专用文件夹
        return "annotation_data/datasets/project_" + projectId;
    }

    /**
     * 检查是否需要全量重新生成
     */
    private boolean checkNeedFullRegeneration(Long projectId, AnnotationProjects config, String projectDatasetPath)
    {
        try {
            Path datasetPath = Paths.get(projectDatasetPath);

            // 如果文件夹不存在，需要全量生成
            if (!Files.exists(datasetPath)) {
                log.info("项目 {} 数据集文件夹不存在，需要全量生成", projectId);
                return true;
            }

            // 检查配置文件是否存在
            Path configFile = datasetPath.resolve("export_config.json");
            if (!Files.exists(configFile)) {
                log.info("项目 {} 配置文件不存在，需要全量生成", projectId);
                return true;
            }

            // 读取上次导出的配置，检查关键参数是否变化
            String lastConfigJson = Files.readString(configFile);
            if (isConfigChanged(config, lastConfigJson)) {
                log.info("项目 {} 导出配置已变化，需要全量生成", projectId);
                return true;
            }

            log.info("项目 {} 配置未变化，可以进行增量更新", projectId);
            return false;

        } catch (Exception e) {
            log.warn("检查配置变化失败，项目ID: {}, 默认执行全量生成", projectId, e);
            return true;
        }
    }

    /**
     * 检查配置是否发生变化（影响图像处理的关键参数）
     */
    private boolean isConfigChanged(AnnotationProjects newConfig, String lastConfigJson)
    {
        try {
            // 这里应该解析JSON并比较关键参数
            // 简化实现：比较导出格式、训练集比例等关键参数
            String newConfigKey = generateConfigKey(newConfig);
            return !lastConfigJson.contains(newConfigKey);
        } catch (Exception e) {
            log.warn("比较配置失败，默认认为配置已变化", e);
            return true;
        }
    }

    /**
     * 生成配置关键字符串
     */
    private String generateConfigKey(AnnotationProjects config)
    {
        return String.format("format:%s,train:%.2f,val:%.2f,test:%.2f",
            config.getExportFormat(),
            config.getTrainRatio(),
            config.getValidationRatio(),
            config.getTestRatio());
    }

    /**
     * 清理项目数据集文件夹
     */
    private void clearProjectDatasetFolder(String projectDatasetPath)
    {
        try {
            Path datasetPath = Paths.get(projectDatasetPath);
            if (Files.exists(datasetPath)) {
                // 递归删除文件夹内容
                Files.walk(datasetPath)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
            }
            // 重新创建文件夹
            Files.createDirectories(datasetPath);
            log.info("已清理项目数据集文件夹: {}", projectDatasetPath);
        } catch (Exception e) {
            log.error("清理项目数据集文件夹失败: {}", projectDatasetPath, e);
            throw new RuntimeException("清理数据集文件夹失败", e);
        }
    }

    /**
     * 执行全量生成
     */
    private boolean performFullGeneration(AnnotationExportJobs job, AnnotationProjects config, String projectDatasetPath)
    {
        try {
            Long projectId = job.getProjectId();

            // 1. 查询所有图像数据
            updateJobProgress(job.getJobId(), 25, "查询项目图像数据...");
            List<AnnotationImages> allImages = queryExportImages(projectId, null);

            if (allImages.isEmpty()) {
                log.warn("项目 {} 没有可导出的图像数据", projectId);
                return false;
            }

            // 2. 应用数据集分割
            updateJobProgress(job.getJobId(), 35, "应用数据集分割...");
            Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData = applySplitToImages(allImages, config);

            // 3. 生成数据集文件夹结构
            updateJobProgress(job.getJobId(), 45, "创建数据集文件夹结构...");
            createDatasetFolderStructure(projectDatasetPath, config.getExportFormat());

            // 4. 处理并保存所有图像
            updateJobProgress(job.getJobId(), 55, "处理图像数据...");
            int processedCount = processAndSaveImages(splitData, projectDatasetPath, config, job.getJobId());

            // 5. 生成标注文件
            updateJobProgress(job.getJobId(), 75, "生成标注文件...");
            generateAnnotationFiles(splitData, projectDatasetPath, config, projectId);

            // 6. 保存配置文件
            updateJobProgress(job.getJobId(), 85, "保存配置信息...");
            saveExportConfig(config, projectDatasetPath);

            // 7. 压缩并导出
            updateJobProgress(job.getJobId(), 90, "压缩数据集...");
            String zipFilePath = compressDataset(projectDatasetPath, job.getJobId());

            // 更新任务信息
            job.setOutputPath(zipFilePath);
            job.setJobLog(job.getJobLog() + "\n全量生成完成，处理图像数量: " + processedCount);
            exportJobsService.updateAnnotationExportJobs(job);

            log.info("全量生成完成，项目ID: {}, 处理图像数量: {}", projectId, processedCount);
            return true;

        } catch (Exception e) {
            log.error("全量生成失败，项目ID: {}", job.getProjectId(), e);
            return false;
        }
    }

    /**
     * 执行增量更新
     */
    private boolean performIncrementalUpdate(AnnotationExportJobs job, AnnotationProjects config, String projectDatasetPath)
    {
        try {
            Long projectId = job.getProjectId();

            // 1. 读取上次更新时间
            updateJobProgress(job.getJobId(), 25, "检查更新时间...");
            LocalDateTime lastUpdateTime = getLastUpdateTime(projectDatasetPath);

            // 2. 查询需要更新的图像（基于update_time）
            updateJobProgress(job.getJobId(), 35, "查询更新的图像...");
            List<AnnotationImages> updatedImages = queryUpdatedImages(projectId, lastUpdateTime);

            if (updatedImages.isEmpty()) {
                log.info("项目 {} 没有需要更新的图像数据", projectId);
                // 直接压缩现有数据集
                updateJobProgress(job.getJobId(), 90, "压缩现有数据集...");
                String zipFilePath = compressDataset(projectDatasetPath, job.getJobId());
                job.setOutputPath(zipFilePath);
                job.setJobLog(job.getJobLog() + "\n增量更新完成，无新增图像");
                exportJobsService.updateAnnotationExportJobs(job);
                return true;
            }

            // 3. 应用数据集分割到更新的图像
            updateJobProgress(job.getJobId(), 45, "处理更新的图像...");
            Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> updatedSplitData = applySplitToImages(updatedImages, config);

            // 4. 增量处理图像（只处理新增/修改的图像）
            updateJobProgress(job.getJobId(), 60, "增量处理图像...");
            int processedCount = processAndSaveImages(updatedSplitData, projectDatasetPath, config, job.getJobId());

            // 5. 更新标注文件（增量更新）
            updateJobProgress(job.getJobId(), 75, "更新标注文件...");
            updateAnnotationFiles(updatedSplitData, projectDatasetPath, config, projectId);

            // 6. 更新时间戳
            updateJobProgress(job.getJobId(), 85, "更新时间戳...");
            updateLastUpdateTime(projectDatasetPath);

            // 7. 压缩并导出
            updateJobProgress(job.getJobId(), 90, "压缩数据集...");
            String zipFilePath = compressDataset(projectDatasetPath, job.getJobId());

            // 更新任务信息
            job.setOutputPath(zipFilePath);
            job.setJobLog(job.getJobLog() + "\n增量更新完成，处理图像数量: " + processedCount);
            exportJobsService.updateAnnotationExportJobs(job);

            log.info("增量更新完成，项目ID: {}, 处理图像数量: {}", projectId, processedCount);
            return true;

        } catch (Exception e) {
            log.error("增量更新失败，项目ID: {}", job.getProjectId(), e);
            return false;
        }
    }

    /**
     * 查询需要导出的图像数据
     */
    private List<AnnotationImages> queryExportImages(Long projectId, List<Long> categoryIds)
    {
        // 查询项目关联的分类
        List<Long> projectCategoryIds = projectCategoriesService.selectCategoryIdsByProjectId(projectId);

        // 如果指定了特定分类，则取交集
        if (categoryIds != null && !categoryIds.isEmpty()) {
            projectCategoryIds = projectCategoryIds.stream()
                .filter(categoryIds::contains)
                .collect(Collectors.toList());
        }

        if (projectCategoryIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询这些分类下的所有图像
        List<AnnotationImages> allImages = new ArrayList<>();
        for (Long categoryId : projectCategoryIds) {
            AnnotationImages queryCondition = new AnnotationImages();
            queryCondition.setCategoryId(categoryId);
            List<AnnotationImages> categoryImages = imagesService.selectAnnotationImagesList(queryCondition);
            allImages.addAll(categoryImages);
        }

        return allImages;
    }

    /**
     * 应用数据集分割到图像列表
     */
    private Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> applySplitToImages(
            List<AnnotationImages> images, AnnotationProjects config)
    {
        Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData = new HashMap<>();
        splitData.put(DatasetSplitUtils.DatasetType.TRAIN, new ArrayList<>());
        splitData.put(DatasetSplitUtils.DatasetType.VALIDATION, new ArrayList<>());
        splitData.put(DatasetSplitUtils.DatasetType.TEST, new ArrayList<>());

        for (AnnotationImages image : images) {
            DatasetSplitUtils.SplitResult splitResult = DatasetSplitUtils.splitDataset(
                image.getImageId(),
                config.getTrainRatio(),
                config.getValidationRatio(),
                config.getTestRatio()
            );

            splitData.get(splitResult.getType()).add(image);
        }

        return splitData;
    }

    /**
     * 准备图像信息数组
     */
    private DatasetExportNative.ImageInfo[] prepareImageInfos(
            Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData)
    {
        List<DatasetExportNative.ImageInfo> imageInfoList = new ArrayList<>();

        for (Map.Entry<DatasetSplitUtils.DatasetType, List<AnnotationImages>> entry : splitData.entrySet()) {
            DatasetSplitUtils.DatasetType datasetType = entry.getKey();
            List<AnnotationImages> images = entry.getValue();

            for (AnnotationImages image : images) {
                DatasetExportNative.ImageInfo imageInfo = new DatasetExportNative.ImageInfo();
                imageInfo.imageId = image.getImageId();
                imageInfo.imagePath = image.getStoragePath(); // 使用storagePath字段
                imageInfo.annotationData = "{}"; // 这里需要查询实际的标注数据
                imageInfo.datasetType = convertDatasetType(datasetType);
                imageInfo.originalWidth = image.getWidth() != null ? image.getWidth().intValue() : 0;
                imageInfo.originalHeight = image.getHeight() != null ? image.getHeight().intValue() : 0;

                imageInfoList.add(imageInfo);
            }
        }

        return imageInfoList.toArray(new DatasetExportNative.ImageInfo[0]);
    }

    /**
     * 转换数据集类型
     */
    private String convertDatasetType(DatasetSplitUtils.DatasetType type)
    {
        switch (type) {
            case TRAIN: return "train";
            case VALIDATION: return "val";
            case TEST: return "test";
            default: return "train";
        }
    }

    /**
     * 准备分类信息JSON
     */
    private String prepareCategoriesJson(Long projectId)
    {
        // 查询项目关联的分类
        List<Long> categoryIds = projectCategoriesService.selectCategoryIdsByProjectId(projectId);

        // 构建简单的分类JSON
        StringBuilder json = new StringBuilder();
        json.append("[");
        for (int i = 0; i < categoryIds.size(); i++) {
            if (i > 0) json.append(",");
            json.append("{\"id\":").append(categoryIds.get(i))
               .append(",\"name\":\"category_").append(categoryIds.get(i)).append("\"}");
        }
        json.append("]");

        return json.toString();
    }

    /**
     * 准备导出配置
     */
    private DatasetExportNative.ExportConfig prepareExportConfig(AnnotationProjects config)
    {
        DatasetExportNative.ExportConfig exportConfig = new DatasetExportNative.ExportConfig();
        exportConfig.exportFormat = config.getExportFormat();
        exportConfig.outputPath = config.getExportPath();
        exportConfig.targetWidth = 640; // 默认值，可以从配置中读取
        exportConfig.targetHeight = 640;
        exportConfig.transformations = "{}"; // 默认无变换
        exportConfig.grayscale = false;
        exportConfig.enableMask = false;
        exportConfig.rotationAngle = 0.0;
        exportConfig.scaleRatio = 1.0;

        return exportConfig;
    }

    /**
     * 调用本地库执行导出
     */
    private DatasetExportNative.ExportResult callNativeExport(
            DatasetExportNative.ExportConfig config,
            DatasetExportNative.ImageInfo[] images,
            String categories,
            String format)
    {
        try {
            switch (format.toLowerCase()) {
                case "coco":
                    return exportNative.exportCOCO(config, images, categories);
                case "voc":
                    return exportNative.exportVOC(config, images, categories);
                case "yolo":
                    return exportNative.exportYOLO(config, images, categories);
                default:
                    DatasetExportNative.ExportResult errorResult = new DatasetExportNative.ExportResult();
                    errorResult.success = false;
                    errorResult.message = "不支持的导出格式: " + format;
                    return errorResult;
            }
        } catch (Exception e) {
            log.error("调用本地库导出失败", e);
            DatasetExportNative.ExportResult errorResult = new DatasetExportNative.ExportResult();
            errorResult.success = false;
            errorResult.message = "本地库调用异常: " + e.getMessage();
            return errorResult;
        }
    }

    /**
     * 更新任务状态
     */
    private void updateJobStatus(AnnotationExportJobs job, AnnotationStatus status, String message)
    {
        job.setStatus(status);
        job.setJobLog(job.getJobLog() + "\n" + LocalDateTime.now() + ": " + message);
        exportJobsService.updateAnnotationExportJobs(job);
    }

    /**
     * 更新任务进度
     */
    private void updateJobProgress(Long jobId, int progress, String message)
    {
        taskProgressCache.put(jobId, progress);
        log.info("任务 {} 进度: {}% - {}", jobId, progress, message);
    }

    // 实现接口中的其他必需方法
    @Override
    public boolean cancelExportTask(Long jobId)
    {
        cancelledTasks.add(jobId);
        AnnotationExportJobs job = exportJobsService.selectAnnotationExportJobsByJobId(jobId);
        if (job != null) {
            updateJobStatus(job, AnnotationStatus.REJECTED, "任务已取消");
        }
        return true;
    }

    @Override
    public ExportTaskStatus getExportTaskStatus(Long jobId)
    {
        AnnotationExportJobs job = exportJobsService.selectAnnotationExportJobsByJobId(jobId);
        if (job == null) {
            return null;
        }

        // 将AnnotationStatus映射到ExportTaskStatus
        switch (job.getStatus()) {
            case NOT_ANNOTATED:
                return ExportTaskStatus.PENDING;
            case IN_PROGRESS:
                return ExportTaskStatus.RUNNING;
            case APPROVED:
                return ExportTaskStatus.COMPLETED;
            case REJECTED:
                return ExportTaskStatus.FAILED;
            default:
                return ExportTaskStatus.PENDING;
        }
    }

    @Override
    public AnnotationExportJobs getExportTaskDetails(Long jobId)
    {
        return exportJobsService.selectAnnotationExportJobsByJobId(jobId);
    }

    @Override
    public List<AnnotationExportJobs> getProjectExportTasks(Long projectId, ExportTaskStatus status)
    {
        AnnotationExportJobs queryCondition = new AnnotationExportJobs();
        queryCondition.setProjectId(projectId);

        List<AnnotationExportJobs> allTasks = exportJobsService.selectAnnotationExportJobsList(queryCondition);

        if (status == null) {
            return allTasks;
        }

        // 根据状态过滤
        return allTasks.stream()
            .filter(task -> {
                ExportTaskStatus taskStatus = getExportTaskStatus(task.getJobId());
                return status.equals(taskStatus);
            })
            .collect(Collectors.toList());
    }

    @Override
    public int cleanupExpiredExportTasks(int retentionDays)
    {
        // 这里应该实现清理逻辑
        log.info("清理过期导出任务，保留天数: {}", retentionDays);
        return 0;
    }

    @Override
    public int getExportProgress(Long jobId)
    {
        return taskProgressCache.getOrDefault(jobId, 0);
    }

    @Override
    public boolean retryExportTask(Long jobId)
    {
        cancelledTasks.remove(jobId);
        return executeExportTask(jobId);
    }

    @Override
    public List<Long> batchCreateExportTasks(List<Long> projectIds, boolean incrementalExport)
    {
        List<Long> taskIds = new ArrayList<>();
        for (Long projectId : projectIds) {
            try {
                Long taskId = createExportTask(projectId, null, incrementalExport);
                taskIds.add(taskId);
            } catch (Exception e) {
                log.error("批量创建导出任务失败，项目ID: {}", projectId, e);
            }
        }
        return taskIds;
    }

    @Override
    public List<String> getSupportedExportFormats()
    {
        return Arrays.asList("coco", "voc", "yolo");
    }

    @Override
    public ExportEstimation estimateExportTask(Long projectId, List<Long> categoryIds)
    {
        // 简单的预估逻辑
        List<AnnotationImages> images = queryExportImagesForEstimation(projectId, categoryIds);

        int totalImages = images.size();
        int totalAnnotations = totalImages * 5; // 假设每张图片平均5个标注
        long estimatedDurationMinutes = totalImages / 100; // 假设每分钟处理100张图片
        long estimatedFileSizeMB = totalImages / 10; // 假设每10张图片1MB

        return new ExportEstimation(estimatedDurationMinutes, estimatedFileSizeMB, totalImages, totalAnnotations);
    }

    /**
     * 查询用于预估的图像数据
     */
    private List<AnnotationImages> queryExportImagesForEstimation(Long projectId, List<Long> categoryIds)
    {
        // 这里应该实现实际的查询逻辑
        // 暂时返回空列表
        return new ArrayList<>();
    }

    /**
     * 查询更新的图像（基于update_time）
     */
    private List<AnnotationImages> queryUpdatedImages(Long projectId, LocalDateTime lastUpdateTime)
    {
        // 查询项目关联的分类
        List<Long> projectCategoryIds = projectCategoriesService.selectCategoryIdsByProjectId(projectId);

        if (projectCategoryIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询这些分类下在指定时间之后更新的图像
        List<AnnotationImages> updatedImages = new ArrayList<>();
        for (Long categoryId : projectCategoryIds) {
            AnnotationImages queryCondition = new AnnotationImages();
            queryCondition.setCategoryId(categoryId);
            // 这里需要添加时间条件查询，暂时查询所有
            List<AnnotationImages> categoryImages = imagesService.selectAnnotationImagesList(queryCondition);

            // 过滤出更新时间晚于lastUpdateTime的图像
            List<AnnotationImages> recentImages = categoryImages.stream()
                .filter(image -> {
                    try {
                        // 假设uploadedAt是String类型，需要转换为LocalDateTime进行比较
                        if (image.getUploadedAt() != null) {
                            LocalDateTime imageTime = LocalDateTime.parse(image.getUploadedAt().toString());
                            return imageTime.isAfter(lastUpdateTime);
                        }
                        return false;
                    } catch (Exception e) {
                        return false; // 解析失败则跳过
                    }
                })
                .collect(Collectors.toList());

            updatedImages.addAll(recentImages);
        }

        return updatedImages;
    }

    /**
     * 获取上次更新时间
     */
    private LocalDateTime getLastUpdateTime(String projectDatasetPath)
    {
        try {
            Path timestampFile = Paths.get(projectDatasetPath, "last_update.txt");
            if (Files.exists(timestampFile)) {
                String timeStr = Files.readString(timestampFile);
                return LocalDateTime.parse(timeStr);
            }
        } catch (Exception e) {
            log.warn("读取上次更新时间失败: {}", projectDatasetPath, e);
        }
        // 如果文件不存在或读取失败，返回很早的时间，触发全量更新
        return LocalDateTime.of(2000, 1, 1, 0, 0);
    }

    /**
     * 更新最后更新时间
     */
    private void updateLastUpdateTime(String projectDatasetPath)
    {
        try {
            Path timestampFile = Paths.get(projectDatasetPath, "last_update.txt");
            Files.writeString(timestampFile, LocalDateTime.now().toString());
        } catch (Exception e) {
            log.error("更新时间戳失败: {}", projectDatasetPath, e);
        }
    }

    /**
     * 创建数据集文件夹结构
     */
    private void createDatasetFolderStructure(String projectDatasetPath, String exportFormat)
    {
        try {
            Path basePath = Paths.get(projectDatasetPath);

            // 创建基础文件夹结构
            Files.createDirectories(basePath.resolve("images/train"));
            Files.createDirectories(basePath.resolve("images/val"));
            Files.createDirectories(basePath.resolve("images/test"));

            // 根据导出格式创建对应的标注文件夹
            switch (exportFormat.toLowerCase()) {
                case "coco":
                    Files.createDirectories(basePath.resolve("annotations"));
                    break;
                case "voc":
                    Files.createDirectories(basePath.resolve("annotations/train"));
                    Files.createDirectories(basePath.resolve("annotations/val"));
                    Files.createDirectories(basePath.resolve("annotations/test"));
                    break;
                case "yolo":
                    Files.createDirectories(basePath.resolve("labels/train"));
                    Files.createDirectories(basePath.resolve("labels/val"));
                    Files.createDirectories(basePath.resolve("labels/test"));
                    break;
            }

            log.info("创建数据集文件夹结构完成: {}", projectDatasetPath);
        } catch (Exception e) {
            log.error("创建数据集文件夹结构失败: {}", projectDatasetPath, e);
            throw new RuntimeException("创建文件夹结构失败", e);
        }
    }

    /**
     * 处理并保存图像（批量优化版本）
     */
    private int processAndSaveImages(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                                   String projectDatasetPath, AnnotationProjects config, Long jobId)
    {
        int totalProcessed = 0;

        // 检查是否需要图像变换
        boolean needTransformation = needImageTransformation(config);

        if (needTransformation && exportNative != null) {
            // 使用JNI批量处理图像变换
            totalProcessed = batchProcessImagesWithJNI(splitData, projectDatasetPath, config, jobId);
        } else {
            // 简单文件复制（无变换需求）
            totalProcessed = batchCopyImages(splitData, projectDatasetPath, jobId);
        }

        return totalProcessed;
    }

    /**
     * 检查是否需要图像变换
     */
    private boolean needImageTransformation(AnnotationProjects config)
    {
        // 检查是否有变换需求：尺寸调整、变换配置等
        return (config.getWidth() != null && config.getWidth() > 0) ||
               (config.getHeight() != null && config.getHeight() > 0) ||
               (config.getTransformations() != null && !config.getTransformations().trim().isEmpty() &&
                !config.getTransformations().equals("{}"));
    }

    /**
     * 使用JNI批量处理图像变换
     */
    private int batchProcessImagesWithJNI(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                                        String projectDatasetPath, AnnotationProjects config, Long jobId)
    {
        int totalProcessed = 0;

        for (Map.Entry<DatasetSplitUtils.DatasetType, List<AnnotationImages>> entry : splitData.entrySet()) {
            String datasetType = convertDatasetType(entry.getKey());
            List<AnnotationImages> images = entry.getValue();

            if (images.isEmpty()) continue;

            // 准备批量处理配置
            DatasetExportNative.BatchTransformConfig batchConfig = prepareBatchTransformConfig(
                images, projectDatasetPath, datasetType, config);

            updateJobProgress(jobId, 55 + (totalProcessed * 15 / getTotalImageCount(splitData)),
                            "批量处理 " + datasetType + " 数据集图像...");

            try {
                // 调用JNI批量处理
                DatasetExportNative.BatchProcessResult result = exportNative.batchApplyImageTransformations(batchConfig);

                if (result.success) {
                    totalProcessed += result.successCount;
                    log.info("批量处理 {} 数据集完成，成功: {}, 失败: {}, 耗时: {}ms",
                            datasetType, result.successCount, result.failedCount, result.processingTimeMs);

                    if (result.failedCount > 0) {
                        log.warn("批量处理中有 {} 张图像失败: {}", result.failedCount,
                                String.join(", ", result.failedPaths));
                    }
                } else {
                    log.error("批量处理 {} 数据集失败: {}", datasetType, result.message);
                    // 降级到单张处理
                    totalProcessed += fallbackToSingleImageProcessing(images, projectDatasetPath, datasetType, config);
                }

            } catch (Exception e) {
                log.error("JNI批量处理失败，降级到单张处理: {}", datasetType, e);
                // 降级到单张处理
                totalProcessed += fallbackToSingleImageProcessing(images, projectDatasetPath, datasetType, config);
            }
        }

        return totalProcessed;
    }

    /**
     * 批量复制图像（无变换）
     */
    private int batchCopyImages(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                              String projectDatasetPath, Long jobId)
    {
        int totalProcessed = 0;
        int totalImages = getTotalImageCount(splitData);

        for (Map.Entry<DatasetSplitUtils.DatasetType, List<AnnotationImages>> entry : splitData.entrySet()) {
            String datasetType = convertDatasetType(entry.getKey());
            List<AnnotationImages> images = entry.getValue();

            updateJobProgress(jobId, 55 + (totalProcessed * 15 / totalImages),
                            "复制 " + datasetType + " 数据集图像...");

            // 批量复制文件
            List<AnnotationImages> successfulCopies = batchCopyImageFiles(images, projectDatasetPath, datasetType);
            totalProcessed += successfulCopies.size();

            if (successfulCopies.size() < images.size()) {
                log.warn("复制 {} 数据集时有 {} 张图像失败", datasetType, images.size() - successfulCopies.size());
            }
        }

        return totalProcessed;
    }

    /**
     * 准备批量变换配置
     */
    private DatasetExportNative.BatchTransformConfig prepareBatchTransformConfig(
            List<AnnotationImages> images, String projectDatasetPath, String datasetType, AnnotationProjects config)
    {
        DatasetExportNative.BatchTransformConfig batchConfig = new DatasetExportNative.BatchTransformConfig();

        // 准备源路径和目标路径数组
        batchConfig.sourcePaths = new String[images.size()];
        batchConfig.targetPaths = new String[images.size()];

        for (int i = 0; i < images.size(); i++) {
            AnnotationImages image = images.get(i);
            batchConfig.sourcePaths[i] = Paths.get("annotation_data", image.getStoragePath()).toString();
            batchConfig.targetPaths[i] = Paths.get(projectDatasetPath, "images", datasetType, image.getOriginalFilename()).toString();
        }

        // 设置变换参数（使用AnnotationProjects中的实际字段）
        batchConfig.targetWidth = config.getWidth() != null ? config.getWidth().intValue() : 0;
        batchConfig.targetHeight = config.getHeight() != null ? config.getHeight().intValue() : 0;
        batchConfig.grayscale = false; // 默认不转灰度，可以从transformations JSON中解析
        batchConfig.enableMask = false; // 默认不启用mask
        batchConfig.rotationAngle = 0.0; // 默认不旋转
        batchConfig.scaleRatio = 1.0; // 默认不缩放
        batchConfig.transformations = config.getTransformations() != null ? config.getTransformations() : "{}";

        // 设置并行处理线程数（根据CPU核心数调整）
        batchConfig.threadCount = Math.min(Runtime.getRuntime().availableProcessors(), Math.max(1, images.size() / 50));

        return batchConfig;
    }

    /**
     * 获取总图像数量
     */
    private int getTotalImageCount(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData)
    {
        return splitData.values().stream().mapToInt(List::size).sum();
    }

    /**
     * 降级到单张图像处理
     */
    private int fallbackToSingleImageProcessing(List<AnnotationImages> images, String projectDatasetPath,
                                              String datasetType, AnnotationProjects config)
    {
        int processed = 0;
        for (AnnotationImages image : images) {
            try {
                copyImageToDataset(image, projectDatasetPath, datasetType);
                processed++;
            } catch (Exception e) {
                log.error("单张图像处理失败: {}", image.getImageId(), e);
            }
        }
        return processed;
    }

    /**
     * 批量复制图像文件
     */
    private List<AnnotationImages> batchCopyImageFiles(List<AnnotationImages> images, String projectDatasetPath, String datasetType)
    {
        List<AnnotationImages> successfulCopies = new ArrayList<>();

        for (AnnotationImages image : images) {
            try {
                copyImageToDataset(image, projectDatasetPath, datasetType);
                successfulCopies.add(image);
            } catch (Exception e) {
                log.error("复制图像文件失败: {}", image.getImageId(), e);
            }
        }

        return successfulCopies;
    }

    /**
     * 复制图像到数据集文件夹
     */
    private void copyImageToDataset(AnnotationImages image, String projectDatasetPath, String datasetType)
    {
        try {
            Path sourcePath = Paths.get("annotation_data", image.getStoragePath());
            Path targetPath = Paths.get(projectDatasetPath, "images", datasetType, image.getOriginalFilename());

            // 确保目标目录存在
            Files.createDirectories(targetPath.getParent());

            // 复制文件
            Files.copy(sourcePath, targetPath, java.nio.file.StandardCopyOption.REPLACE_EXISTING);
        } catch (Exception e) {
            log.error("复制图像文件失败: {} -> {}", image.getStoragePath(), projectDatasetPath, e);
            throw new RuntimeException("复制图像文件失败", e);
        }
    }

    /**
     * 生成标注文件
     */
    private void generateAnnotationFiles(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                                       String projectDatasetPath, AnnotationProjects config, Long projectId)
    {
        try {
            String exportFormat = config.getExportFormat().toLowerCase();

            switch (exportFormat) {
                case "coco":
                    generateCocoAnnotations(splitData, projectDatasetPath, projectId);
                    break;
                case "voc":
                    generateVocAnnotations(splitData, projectDatasetPath, projectId);
                    break;
                case "yolo":
                    generateYoloAnnotations(splitData, projectDatasetPath, projectId);
                    break;
                default:
                    log.warn("不支持的导出格式: {}", exportFormat);
            }
        } catch (Exception e) {
            log.error("生成标注文件失败", e);
            throw new RuntimeException("生成标注文件失败", e);
        }
    }

    /**
     * 更新标注文件（增量）
     */
    private void updateAnnotationFiles(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> updatedSplitData,
                                     String projectDatasetPath, AnnotationProjects config, Long projectId)
    {
        // 对于增量更新，重新生成所有标注文件以确保一致性
        generateAnnotationFiles(updatedSplitData, projectDatasetPath, config, projectId);
    }

    /**
     * 生成COCO格式标注
     */
    private void generateCocoAnnotations(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                                       String projectDatasetPath, Long projectId)
    {
        // 简化实现，生成基本的COCO JSON文件
        for (Map.Entry<DatasetSplitUtils.DatasetType, List<AnnotationImages>> entry : splitData.entrySet()) {
            String datasetType = convertDatasetType(entry.getKey());
            List<AnnotationImages> images = entry.getValue();

            try {
                Path annotationFile = Paths.get(projectDatasetPath, "annotations", datasetType + ".json");
                String cocoJson = generateCocoJson(images, projectId);
                Files.writeString(annotationFile, cocoJson);
            } catch (Exception e) {
                log.error("生成COCO标注文件失败: {}", datasetType, e);
            }
        }
    }

    /**
     * 生成VOC格式标注
     */
    private void generateVocAnnotations(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                                      String projectDatasetPath, Long projectId)
    {
        // 简化实现，为每个图像生成XML文件
        for (Map.Entry<DatasetSplitUtils.DatasetType, List<AnnotationImages>> entry : splitData.entrySet()) {
            String datasetType = convertDatasetType(entry.getKey());
            List<AnnotationImages> images = entry.getValue();

            for (AnnotationImages image : images) {
                try {
                    String xmlContent = generateVocXml(image, projectId);
                    String filename = image.getOriginalFilename().replaceAll("\\.[^.]+$", ".xml");
                    Path xmlFile = Paths.get(projectDatasetPath, "annotations", datasetType, filename);
                    Files.writeString(xmlFile, xmlContent);
                } catch (Exception e) {
                    log.error("生成VOC标注文件失败: {}", image.getImageId(), e);
                }
            }
        }
    }

    /**
     * 生成YOLO格式标注
     */
    private void generateYoloAnnotations(Map<DatasetSplitUtils.DatasetType, List<AnnotationImages>> splitData,
                                       String projectDatasetPath, Long projectId)
    {
        // 简化实现，为每个图像生成txt文件
        for (Map.Entry<DatasetSplitUtils.DatasetType, List<AnnotationImages>> entry : splitData.entrySet()) {
            String datasetType = convertDatasetType(entry.getKey());
            List<AnnotationImages> images = entry.getValue();

            for (AnnotationImages image : images) {
                try {
                    String txtContent = generateYoloTxt(image, projectId);
                    String filename = image.getOriginalFilename().replaceAll("\\.[^.]+$", ".txt");
                    Path txtFile = Paths.get(projectDatasetPath, "labels", datasetType, filename);
                    Files.writeString(txtFile, txtContent);
                } catch (Exception e) {
                    log.error("生成YOLO标注文件失败: {}", image.getImageId(), e);
                }
            }
        }
    }

    /**
     * 生成COCO JSON内容
     */
    private String generateCocoJson(List<AnnotationImages> images, Long projectId)
    {
        // 简化的COCO JSON生成
        StringBuilder json = new StringBuilder();
        json.append("{\"images\":[");

        for (int i = 0; i < images.size(); i++) {
            if (i > 0) json.append(",");
            AnnotationImages image = images.get(i);
            json.append("{\"id\":").append(image.getImageId())
                .append(",\"file_name\":\"").append(image.getOriginalFilename()).append("\"")
                .append(",\"width\":").append(image.getWidth() != null ? image.getWidth() : 640)
                .append(",\"height\":").append(image.getHeight() != null ? image.getHeight() : 640)
                .append("}");
        }

        json.append("],\"annotations\":[],\"categories\":[]}");
        return json.toString();
    }

    /**
     * 生成VOC XML内容
     */
    private String generateVocXml(AnnotationImages image, Long projectId)
    {
        // 简化的VOC XML生成
        return String.format(
            "<annotation><filename>%s</filename><size><width>%d</width><height>%d</height></size></annotation>",
            image.getOriginalFilename(),
            image.getWidth() != null ? image.getWidth().intValue() : 640,
            image.getHeight() != null ? image.getHeight().intValue() : 640
        );
    }

    /**
     * 生成YOLO TXT内容
     */
    private String generateYoloTxt(AnnotationImages image, Long projectId)
    {
        // 简化的YOLO格式生成（空文件，表示无标注）
        return "";
    }

    /**
     * 保存导出配置
     */
    private void saveExportConfig(AnnotationProjects config, String projectDatasetPath)
    {
        try {
            Path configFile = Paths.get(projectDatasetPath, "export_config.json");
            String configJson = generateConfigJson(config);
            Files.writeString(configFile, configJson);
        } catch (Exception e) {
            log.error("保存导出配置失败: {}", projectDatasetPath, e);
            throw new RuntimeException("保存导出配置失败", e);
        }
    }

    /**
     * 生成配置JSON
     */
    private String generateConfigJson(AnnotationProjects config)
    {
        return String.format(
            "{\"exportFormat\":\"%s\",\"trainRatio\":%.2f,\"validationRatio\":%.2f,\"testRatio\":%.2f,\"configKey\":\"%s\"}",
            config.getExportFormat(),
            config.getTrainRatio(),
            config.getValidationRatio(),
            config.getTestRatio(),
            generateConfigKey(config)
        );
    }

    /**
     * 压缩数据集文件夹
     */
    private String compressDataset(String projectDatasetPath, Long jobId)
    {
        try {
            String zipFileName = "dataset_" + jobId + "_" + System.currentTimeMillis() + ".zip";
            String zipFilePath = "annotation_data/exports/" + zipFileName;

            // 确保导出目录存在
            Path exportDir = Paths.get("annotation_data/exports");
            Files.createDirectories(exportDir);

            // 创建ZIP文件
            try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFilePath))) {
                Path sourcePath = Paths.get(projectDatasetPath);
                Files.walk(sourcePath)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        try {
                            String entryName = sourcePath.relativize(path).toString().replace("\\", "/");
                            ZipEntry zipEntry = new ZipEntry(entryName);
                            zos.putNextEntry(zipEntry);

                            try (FileInputStream fis = new FileInputStream(path.toFile())) {
                                byte[] buffer = new byte[1024];
                                int length;
                                while ((length = fis.read(buffer)) > 0) {
                                    zos.write(buffer, 0, length);
                                }
                            }
                            zos.closeEntry();
                        } catch (Exception e) {
                            log.error("压缩文件失败: {}", path, e);
                        }
                    });
            }

            log.info("数据集压缩完成: {}", zipFilePath);
            return zipFilePath;

        } catch (Exception e) {
            log.error("压缩数据集失败: {}", projectDatasetPath, e);
            throw new RuntimeException("压缩数据集失败", e);
        }
    }
}

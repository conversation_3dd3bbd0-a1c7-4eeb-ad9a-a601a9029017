package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationExportJobsMapper;
import com.ylzx.annotation.domain.AnnotationExportJobs;
import com.ylzx.annotation.service.AnnotationExportJobsService;

/**
 * 标注导出任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationExportJobsServiceImpl extends ServiceImpl<AnnotationExportJobsMapper,AnnotationExportJobs> implements AnnotationExportJobsService
{
    @Autowired
    private AnnotationExportJobsMapper annotationExportJobsMapper;

    /**
     * 查询标注导出任务
     * 
     * @param jobId 标注导出任务主键
     * @return 标注导出任务
     */
    @Override
    public AnnotationExportJobs selectAnnotationExportJobsByJobId(Long jobId)
    {
        return annotationExportJobsMapper.selectAnnotationExportJobsByJobId(jobId);
    }

    /**
     * 查询标注导出任务列表
     * 
     * @param annotationExportJobs 标注导出任务
     * @return 标注导出任务集合
     */
    @Override
    public List<AnnotationExportJobs> selectAnnotationExportJobsList(AnnotationExportJobs annotationExportJobs)
    {
        return annotationExportJobsMapper.selectAnnotationExportJobsList(annotationExportJobs);
    }

    /**
     * 新增标注导出任务
     * 
     * @param annotationExportJobs 标注导出任务
     * @return 结果
     */
    @Override
    public int insertAnnotationExportJobs(AnnotationExportJobs annotationExportJobs)
    {
        return annotationExportJobsMapper.insertAnnotationExportJobs(annotationExportJobs);
    }

    /**
     * 修改标注导出任务
     * 
     * @param annotationExportJobs 标注导出任务
     * @return 结果
     */
    @Override
    public int updateAnnotationExportJobs(AnnotationExportJobs annotationExportJobs)
    {
        return annotationExportJobsMapper.updateAnnotationExportJobs(annotationExportJobs);
    }

    /**
     * 批量删除标注导出任务
     * 
     * @param jobIds 需要删除的标注导出任务主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationExportJobsByJobIds(Long[] jobIds)
    {
        return annotationExportJobsMapper.deleteAnnotationExportJobsByJobIds(jobIds);
    }

    /**
     * 删除标注导出任务信息
     * 
     * @param jobId 标注导出任务主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationExportJobsByJobId(Long jobId)
    {
        return annotationExportJobsMapper.deleteAnnotationExportJobsByJobId(jobId);
    }
}

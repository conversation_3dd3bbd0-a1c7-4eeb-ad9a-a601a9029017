#ifndef COMMON_H
#define COMMON_H

#include <string>
#include <vector>
#include <memory>

// 导出格式枚举
enum class ExportFormat {
    COCO,
    VOC,
    YOLO
};

// 数据集类型枚举
enum class DatasetType {
    TRAIN,
    VALIDATION,
    TEST
};

// 导出配置结构
struct ExportConfig {
    ExportFormat format;
    std::string outputPath;
    int targetWidth;
    int targetHeight;
    std::string transformations;
    bool grayscale;
    bool enableMask;
    double rotationAngle;
    double scaleRatio;
};

// 图像信息结构
struct ImageInfo {
    long imageId;
    std::string imagePath;
    std::string annotationData;
    DatasetType datasetType;
    int originalWidth;
    int originalHeight;
};

// 导出结果结构
struct ExportResult {
    bool success;
    std::string message;
    int processedCount;
    std::string outputPath;
};

// 工具函数
std::string formatToString(ExportFormat format);
ExportFormat stringToFormat(const std::string& format);
std::string datasetTypeToString(DatasetType type);
DatasetType stringToDatasetType(const std::string& type);

#endif // COMMON_H

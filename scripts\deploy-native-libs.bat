@echo off
REM 部署本地库文件到resources目录的脚本

echo 部署本地库文件...

set RESOURCES_DIR=ylzx-annotation\src\main\resources\native
set BUILD_DIR=native\build\bin

REM 检查构建目录是否存在
if not exist "%BUILD_DIR%" (
    echo 错误: 构建目录不存在: %BUILD_DIR%
    echo 请先编译本地库
    pause
    exit /b 1
)

REM 检查资源目录是否存在
if not exist "%RESOURCES_DIR%" (
    echo 创建资源目录: %RESOURCES_DIR%
    mkdir "%RESOURCES_DIR%"
)

REM 创建架构特定目录
if not exist "%RESOURCES_DIR%\amd64" mkdir "%RESOURCES_DIR%\amd64"
if not exist "%RESOURCES_DIR%\aarch64" mkdir "%RESOURCES_DIR%\aarch64"

REM 复制Windows库文件
if exist "%BUILD_DIR%\dataset_export.dll" (
    echo 复制Windows库文件...
    copy "%BUILD_DIR%\dataset_export.dll" "%RESOURCES_DIR%\dataset_export.dll"
    copy "%BUILD_DIR%\dataset_export.dll" "%RESOURCES_DIR%\amd64\dataset_export.dll"
    echo Windows库文件复制完成
) else (
    echo 警告: 找不到Windows库文件: %BUILD_DIR%\dataset_export.dll
)

REM 提示用户添加其他平台的库文件
echo.
echo 请手动添加其他平台的库文件:
echo.
echo Linux x64:   %RESOURCES_DIR%\libdataset_export.so
echo Linux x64:   %RESOURCES_DIR%\amd64\libdataset_export.so
echo Linux ARM64: %RESOURCES_DIR%\aarch64\libdataset_export.so
echo.
echo macOS x64:   %RESOURCES_DIR%\libdataset_export.dylib  
echo macOS x64:   %RESOURCES_DIR%\amd64\libdataset_export.dylib
echo macOS ARM64: %RESOURCES_DIR%\aarch64\libdataset_export.dylib
echo.

REM 显示当前库文件
echo 当前库文件:
dir "%RESOURCES_DIR%" /s /b | findstr /E ".dll .so .dylib"

echo.
echo 部署完成！
pause

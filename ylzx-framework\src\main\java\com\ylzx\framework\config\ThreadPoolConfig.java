package com.ylzx.framework.config;

import com.ylzx.common.utils.Threads;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.Data;

/**
 * 线程池配置
 *
 * <AUTHOR>
 **/
@Configuration
@EnableConfigurationProperties(ThreadPoolConfig.ThreadPoolProperties.class)
public class ThreadPoolConfig
{

    private final ThreadPoolProperties properties;

    public ThreadPoolConfig(ThreadPoolProperties properties) {
        this.properties = properties;
    }

    /**
     * Spring Boot应用中推荐使用的标准平台线程池，适用于CPU密集型任务。
     * 通过 'thread.pool' 前缀在配置文件中进行配置。
     * @return ThreadPoolTaskExecutor 实例
     */
    @Bean(name = "threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor()
    {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.getCorePoolSize());
        executor.setMaxPoolSize(properties.getMaxPoolSize());
        executor.setQueueCapacity(properties.getQueueCapacity());
        executor.setKeepAliveSeconds(properties.getKeepAliveSeconds());
        executor.setThreadNamePrefix("platform-task-");
        // 配置优雅停机
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(properties.getAwaitTerminationSeconds());
        // 线程池对拒绝任务(无线程可用)的处理策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 周期性或定时任务执行器。
     * 使用平台线程，因为虚拟线程不支持定时任务调度。
     * @return ScheduledExecutorService 实例
     */
    @Bean(name = "scheduledExecutorService", destroyMethod = "shutdown")
    protected ScheduledExecutorService scheduledExecutorService()
    {
        return new ScheduledThreadPoolExecutor(properties.getCorePoolSize(),
                new BasicThreadFactory.Builder().namingPattern("schedule-pool-%d").daemon(true).build(),
                new ThreadPoolExecutor.CallerRunsPolicy())
        {
            @Override
            protected void afterExecute(Runnable r, Throwable t)
            {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };
    }

    /**
     * Java 21 虚拟线程池。
     * 专门用于执行大量的、短期的、I/O密集型任务（如文件读写、网络请求）。
     * 它能极大地提高I/O密集型应用的吞吐量。
     *
     * @return ExecutorService 实例
     */
    @Bean
    public ExecutorService virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }


    @Data
    @ConfigurationProperties(prefix = "thread.pool")
    public static class ThreadPoolProperties {
        /**
         * 核心线程池大小
         */
        private int corePoolSize = 50;
        /**
         * 最大可创建的线程数
         */
        private int maxPoolSize = 200;
        /**
         * 队列最大长度
         */
        private int queueCapacity = 1000;
        /**
         * 线程池维护线程所允许的空闲时间
         */
        private int keepAliveSeconds = 300;
        /**
         * 优雅停机前的最大等待时间（秒）
         */
        private int awaitTerminationSeconds = 60;
    }
}

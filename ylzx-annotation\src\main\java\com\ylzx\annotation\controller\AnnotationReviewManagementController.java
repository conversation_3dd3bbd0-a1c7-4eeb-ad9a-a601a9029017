package com.ylzx.annotation.controller;

import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import com.ylzx.annotation.domain.AnnotationReviews;
import com.ylzx.annotation.domain.vo.ReviewTaskWithDetailsVo;
import com.ylzx.annotation.service.AnnotationReviewsService;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.common.utils.SecurityUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 标注审核管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Tag(name = "标注审核管理", description = "标注审核管理相关接口")
@RestController
@RequestMapping("/annotation/review-management")
@Validated
public class AnnotationReviewManagementController extends BaseController
{
    @Autowired
    private AnnotationReviewsService annotationReviewsService;

    /**
     * 生成抽审记录
     */
    @Operation(summary = "生成抽审记录", description = "根据指定条件生成抽审记录")
    @Log(title = "生成抽审记录", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateReviewRecords(
            @Parameter(description = "标注分类ID", required = false) @RequestParam(required = false) Long categoryId,
            @Parameter(description = "开始时间", required = false) @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = false) @RequestParam(required = false) LocalDateTime endTime,
            @Parameter(description = "抽审比例(0-1之间的小数)", required = false) @RequestParam(required = false) BigDecimal reviewRatio,
            @Parameter(description = "抽审数量", required = false) @RequestParam(required = false) Integer reviewCount)
    {
        // 参数验证
        if (reviewRatio != null && (reviewRatio.compareTo(BigDecimal.ZERO) <= 0 || reviewRatio.compareTo(BigDecimal.ONE) > 0)) {
            return error("抽审比例必须在0-1之间");
        }
        
        if (reviewCount != null && reviewCount <= 0) {
            return error("抽审数量必须大于0");
        }
        
        if (reviewRatio == null && reviewCount == null) {
            return error("抽审比例和抽审数量至少指定一个");
        }

        int generatedCount = annotationReviewsService.generateReviewRecords(
            categoryId, startTime, endTime, reviewRatio, reviewCount);
        
        return success("成功生成 " + generatedCount + " 条抽审记录");
    }

    /**
     * 领取审核任务
     */
    @Operation(summary = "领取审核任务", description = "审核员领取待审核的任务")
    @Log(title = "领取审核任务", businessType = BusinessType.UPDATE)
    @PostMapping("/claim")
    public AjaxResult claimReviewTasks(
            @Parameter(description = "标注分类ID", required = false) @RequestParam(required = false) Long categoryId,
            @Parameter(description = "领取数量", required = true) @RequestParam(defaultValue = "10") int limit)
    {
        if (limit <= 0 || limit > 100) {
            return error("领取数量必须在1-100之间");
        }

        List<AnnotationReviews> claimedTasks = annotationReviewsService.claimReviewTasks(categoryId, limit);

        return AjaxResult.success("成功领取 " + claimedTasks.size() + " 个审核任务").put("data", claimedTasks);
    }

    /**
     * 领取审核任务（返回详细信息，包含图片和标注）
     */
    @Operation(summary = "领取审核任务（返回详细信息）", description = "审核员领取待审核的任务，返回图片和标注详细信息")
    @Log(title = "领取审核任务", businessType = BusinessType.UPDATE)
    @PostMapping("/claim-with-details")
    public AjaxResult claimReviewTasksWithDetails(
            @Parameter(description = "标注分类ID", required = false) @RequestParam(required = false) Long categoryId,
            @Parameter(description = "领取数量", required = true) @RequestParam(defaultValue = "10") int limit)
    {
        if (limit <= 0 || limit > 100) {
            return error("领取数量必须在1-100之间");
        }

        List<ReviewTaskWithDetailsVo> claimedTasks = annotationReviewsService.claimReviewTasksWithDetails(categoryId, limit);

        return AjaxResult.success("成功领取 " + claimedTasks.size() + " 个审核任务").put("data", claimedTasks);
    }

    /**
     * 查询我的审核任务
     */
    @Operation(summary = "查询我的审核任务", description = "查询当前用户的审核任务列表")
    @GetMapping("/my-tasks")
    public AjaxResult getMyReviewTasks(
            @Parameter(description = "标注分类ID", required = false) @RequestParam(required = false) Long categoryId,
            @Parameter(description = "审核状态", required = false) @RequestParam(required = false) String status)
    {
        AnnotationReviews query = new AnnotationReviews();
        query.setAuditor(SecurityUtils.getUsername());
        
        if (status != null && !status.isEmpty()) {
            try {
                query.setStatus(com.ylzx.annotation.domain.enums.AnnotationStatus.valueOf(status));
            } catch (IllegalArgumentException e) {
                return error("无效的审核状态");
            }
        }

        List<AnnotationReviews> myTasks = annotationReviewsService.selectAnnotationReviewsList(query);
        
        return success(myTasks);
    }

    /**
     * 提交审核结果
     */
    @Operation(summary = "提交审核结果", description = "提交标注审核结果")
    @Log(title = "提交审核结果", businessType = BusinessType.UPDATE)
    @PutMapping("/submit/{reviewId}")
    public AjaxResult submitReviewResult(
            @Parameter(description = "审核记录ID", required = true) @PathVariable Long reviewId,
            @Parameter(description = "审核状态", required = true) @RequestParam String status,
            @Parameter(description = "审核意见", required = false) @RequestParam(required = false) String comments)
    {
        // 验证审核状态
        com.ylzx.annotation.domain.enums.AnnotationStatus reviewStatus;
        try {
            reviewStatus = com.ylzx.annotation.domain.enums.AnnotationStatus.valueOf(status);
            if (!reviewStatus.equals(com.ylzx.annotation.domain.enums.AnnotationStatus.APPROVED) && 
                !reviewStatus.equals(com.ylzx.annotation.domain.enums.AnnotationStatus.REJECTED)) {
                return error("审核状态只能是APPROVED或REJECTED");
            }
        } catch (IllegalArgumentException e) {
            return error("无效的审核状态");
        }

        // 查询审核记录
        AnnotationReviews review = annotationReviewsService.selectAnnotationReviewsByReviewId(reviewId);
        if (review == null) {
            return error("审核记录不存在");
        }

        // 验证审核权限
        if (!SecurityUtils.getUsername().equals(review.getAuditor())) {
            return error("只能审核自己领取的任务");
        }

        // 更新审核结果
        review.setStatus(reviewStatus);
        review.setComments(comments);
        review.setAuditTime(LocalDateTime.now());
        
        int result = annotationReviewsService.updateAnnotationReviews(review);
        
        return toAjax(result);
    }

    /**
     * 查询审核统计信息
     */
    @Operation(summary = "查询审核统计信息", description = "查询指定标注人的审核统计信息")
    @GetMapping("/stats/{annotatorId}")
    public AjaxResult getReviewStats(
            @Parameter(description = "标注人ID", required = true) @PathVariable Long annotatorId)
    {
        java.util.Map<String, Object> stats = annotationReviewsService.countReviewStatsByAnnotatorId(annotatorId);
        return success(stats);
    }

    /**
     * 批量生成抽审记录（按分类）
     */
    @Operation(summary = "批量生成抽审记录", description = "为指定分类批量生成抽审记录")
    @Log(title = "批量生成抽审记录", businessType = BusinessType.INSERT)
    @PostMapping("/batch-generate")
    public AjaxResult batchGenerateReviewRecords(
            @Parameter(description = "分类ID列表", required = true) @RequestBody List<Long> categoryIds,
            @Parameter(description = "开始时间", required = false) @RequestParam(required = false) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = false) @RequestParam(required = false) LocalDateTime endTime)
    {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return error("分类ID列表不能为空");
        }

        int totalGenerated = 0;
        for (Long categoryId : categoryIds) {
            int generated = annotationReviewsService.generateReviewRecords(
                categoryId, startTime, endTime, null, null);
            totalGenerated += generated;
        }
        
        return success("成功为 " + categoryIds.size() + " 个分类生成了 " + totalGenerated + " 条抽审记录");
    }

    /**
     * 获取我的审核任务列表（返回详细信息，包含图片和标注）
     */
    @Operation(summary = "获取我的审核任务列表（返回详细信息）", description = "查询当前用户的审核任务列表，包含图片和标注详细信息")
    @GetMapping("/my-tasks-with-details")
    public AjaxResult getMyReviewTasksWithDetails(
            @Parameter(description = "标注分类ID", required = false) @RequestParam(required = false) Long categoryId,
            @Parameter(description = "审核状态列表，逗号分隔", required = false) @RequestParam(required = false) String statuses)
    {
        List<com.ylzx.annotation.domain.enums.AnnotationStatus> statusList = null;
        if (statuses != null && !statuses.trim().isEmpty()) {
            statusList = Arrays.stream(statuses.split(","))
                    .map(String::trim)
                    .map(com.ylzx.annotation.domain.enums.AnnotationStatus::valueOf)
                    .collect(Collectors.toList());
        }

        List<ReviewTaskWithDetailsVo> tasks = annotationReviewsService.getMyReviewTasksWithDetails(categoryId, statusList);
        return success(tasks);
    }

    /**
     * 获取单个审核任务的详细信息（包含图片和标注）
     */
    @Operation(summary = "获取单个审核任务的详细信息", description = "获取指定审核任务的详细信息，包含图片和标注")
    @GetMapping("/task-details/{reviewId}")
    public AjaxResult getReviewTaskDetails(@Parameter(description = "审核任务ID") @PathVariable Long reviewId)
    {
        ReviewTaskWithDetailsVo taskDetails = annotationReviewsService.getReviewTaskWithDetails(reviewId);
        if (taskDetails == null) {
            return error("审核任务不存在");
        }
        return success(taskDetails);
    }
}

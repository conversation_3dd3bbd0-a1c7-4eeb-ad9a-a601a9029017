package com.ylzx.common.core.utils.annotation;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用于解析和格式化标注坐标的工具类.
 * 根据坐标点的数量来区分形状:
 * - 2个点: RECTANGLE (e.g., "x1 y1, x2 y2")
 * - 3个或更多点: POLYGON (e.g., "x1 y1, x2 y2, x3 y3, ...")
 *
 * <AUTHOR>
 */
public final class AnnotationCoordinateParser {

    private AnnotationCoordinateParser() {
        // 工具类，私有化构造函数
    }

    /**
     * 将坐标字符串解析为Shape对象.
     *
     * @param coordinateString 坐标字符串
     * @return 相应的 Shape 对象 (Rectangle 或 Polygon)
     * @throws IllegalArgumentException 如果字符串格式无效或点数不足
     */
    public static Shape parse(String coordinateString) {
        if (StringUtils.isBlank(coordinateString)) {
            throw new IllegalArgumentException("坐标字符串不能为空。");
        }

        List<Point> points = parsePoints(coordinateString);

        if (points.size() < 2) {
            throw new IllegalArgumentException("一个形状至少需要2个点，当前点数: " + points.size());
        }

        if (points.size() == 2) {
            return new Rectangle(points.get(0), points.get(1));
        } else {
            return new Polygon(points);
        }
    }

    private static List<Point> parsePoints(String content) {
        if (StringUtils.isBlank(content)) {
            // 返回空列表而不是抛出异常，让主解析方法处理点的数量
            return List.of();
        }
        return Arrays.stream(content.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Point::fromString)
                .collect(Collectors.toList());
    }

    /**
     * 将Shape对象格式化为坐标字符串.
     *
     * @param shape Shape对象
     * @return 坐标格式的字符串
     */
    public static String format(Shape shape) {
        if (shape == null) {
            return null;
        }
        return shape.toWkt();
    }
} 
package com.ylzx.annotation.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ylzx.annotation.mapper.AnnotationProjectCategoriesMapper;
import com.ylzx.annotation.domain.AnnotationProjectCategories;
import com.ylzx.annotation.service.AnnotationProjectCategoriesService;
import lombok.extern.slf4j.Slf4j;

/**
 * 标注项目分类关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Service
public class AnnotationProjectCategoriesServiceImpl extends ServiceImpl<AnnotationProjectCategoriesMapper, AnnotationProjectCategories> implements AnnotationProjectCategoriesService
{
    @Autowired
    private AnnotationProjectCategoriesMapper annotationProjectCategoriesMapper;

    /**
     * 根据项目ID查询关联的分类ID列表
     * 
     * @param projectId 项目ID
     * @return 分类ID列表
     */
    @Override
    public List<Long> selectCategoryIdsByProjectId(Long projectId)
    {
        return annotationProjectCategoriesMapper.selectCategoryIdsByProjectId(projectId);
    }

    /**
     * 根据分类ID查询关联的项目ID列表
     * 
     * @param categoryId 分类ID
     * @return 项目ID列表
     */
    @Override
    public List<Long> selectProjectIdsByCategoryId(Long categoryId)
    {
        return annotationProjectCategoriesMapper.selectProjectIdsByCategoryId(categoryId);
    }

    /**
     * 批量插入项目分类关联关系
     * 
     * @param projectCategoriesList 项目分类关联列表
     * @return 插入成功的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertBatch(List<AnnotationProjectCategories> projectCategoriesList)
    {
        if (projectCategoriesList == null || projectCategoriesList.isEmpty()) {
            return 0;
        }
        
        // 批量处理，每次处理200条记录
        int batchSize = 200;
        int totalInserted = 0;
        
        for (int i = 0; i < projectCategoriesList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, projectCategoriesList.size());
            List<AnnotationProjectCategories> batch = projectCategoriesList.subList(i, endIndex);
            
            int inserted = annotationProjectCategoriesMapper.insertBatch(batch);
            totalInserted += inserted;
            
            log.debug("批量插入项目分类关联关系，当前批次: {}-{}, 插入数量: {}", i, endIndex - 1, inserted);
        }
        
        log.info("批量插入项目分类关联关系完成，总插入数量: {}", totalInserted);
        return totalInserted;
    }

    /**
     * 根据项目ID删除所有关联关系
     * 
     * @param projectId 项目ID
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByProjectId(Long projectId)
    {
        int deleted = annotationProjectCategoriesMapper.deleteByProjectId(projectId);
        log.info("删除项目ID为{}的所有分类关联关系，删除数量: {}", projectId, deleted);
        return deleted;
    }

    /**
     * 根据分类ID删除所有关联关系
     * 
     * @param categoryId 分类ID
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByCategoryId(Long categoryId)
    {
        int deleted = annotationProjectCategoriesMapper.deleteByCategoryId(categoryId);
        log.info("删除分类ID为{}的所有项目关联关系，删除数量: {}", categoryId, deleted);
        return deleted;
    }

    /**
     * 检查项目和分类的关联关系是否存在
     *
     * @param projectId 项目ID
     * @param categoryId 分类ID
     * @return 是否存在关联关系
     */
    @Override
    public boolean existsRelation(Long projectId, Long categoryId)
    {
        return annotationProjectCategoriesMapper.existsRelation(projectId, categoryId);
    }

    /**
     * 更新项目的分类关联关系
     * 先删除原有关联，再插入新的关联
     * 
     * @param projectId 项目ID
     * @param categoryIds 新的分类ID列表
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProjectCategories(Long projectId, List<Long> categoryIds)
    {
        try {
            // 先删除原有关联关系
            deleteByProjectId(projectId);
            
            // 如果新的分类列表不为空，则插入新的关联关系
            if (categoryIds != null && !categoryIds.isEmpty()) {
                List<AnnotationProjectCategories> newRelations = new ArrayList<>();
                for (Long categoryId : categoryIds) {
                    AnnotationProjectCategories relation = new AnnotationProjectCategories();
                    relation.setProjectId(projectId);
                    relation.setCategoryId(categoryId);
                    relation.setStatus("1"); // 默认状态为有效
                    newRelations.add(relation);
                }
                
                insertBatch(newRelations);
            }
            
            log.info("更新项目ID为{}的分类关联关系成功，新分类数量: {}", projectId, 
                    categoryIds != null ? categoryIds.size() : 0);
            return true;
            
        } catch (Exception e) {
            log.error("更新项目ID为{}的分类关联关系失败", projectId, e);
            throw e;
        }
    }

    /**
     * 批量更新多个项目的分类关联关系
     * 
     * @param projectCategoryMap 项目ID到分类ID列表的映射
     * @return 更新成功的项目数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateProjectCategories(Map<Long, List<Long>> projectCategoryMap)
    {
        if (projectCategoryMap == null || projectCategoryMap.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        
        for (Map.Entry<Long, List<Long>> entry : projectCategoryMap.entrySet()) {
            Long projectId = entry.getKey();
            List<Long> categoryIds = entry.getValue();
            
            try {
                updateProjectCategories(projectId, categoryIds);
                successCount++;
            } catch (Exception e) {
                log.error("批量更新项目ID为{}的分类关联关系失败", projectId, e);
                // 继续处理其他项目，不中断整个批量操作
            }
        }
        
        log.info("批量更新项目分类关联关系完成，成功更新项目数量: {}/{}", successCount, projectCategoryMap.size());
        return successCount;
    }
}

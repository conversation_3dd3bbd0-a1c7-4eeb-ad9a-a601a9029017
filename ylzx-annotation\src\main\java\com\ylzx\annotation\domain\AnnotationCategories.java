package com.ylzx.annotation.domain;

import java.io.Serial;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

/**
 * 标注分类对象 annotation_categories
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationCategories extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注分类主键 */
    private Long categoryId;

    /** 标注分类名称 */
    @Excel(name = "标注分类名称")
    private String name;

    /** 标注分类编码 */
    @Excel(name = "标注分类编码")
    private String code;

    /** 标注分类描述 */
    @Excel(name = "标注分类描述")
    private String description;

    /** 标注分类审核比例 */
    @Excel(name = "标注分类审核比例")
    private BigDecimal reviewRatio;
}

# ylzx-flowable

## 平台简介

`ylzx-flowable` 是一个基于 Spring Boot 和 Flowable 的高性能工作流管理平台。本项目源自 `ruoyi-flowable`，但在其基础上进行了深度的定制和现代化升级，旨在提供一个更纯净、更高效、更贴合现代化开发需求的纯后端开发脚手架。

主要重构和升级内容包括：
*   **全面去RuoYi化**：项目结构和代码已完全重构，移除了原有的 `ruoyi` 依赖，模块统一重命名为 `ylzx-xxx` 格式，使其成为一个独立的、结构更清晰的项目。
*   **核心框架升级**：核心框架从 Spring Boot 2.x 升级至 **Spring Boot 3.x**，全面拥抱 Jakarta EE 新规范，享受新版本带来的性能提升和新特性。
*   **数据库迁移**：数据持久层已从 MySQL 切换为 **PostgreSQL**，以适应更多样化的企业级应用场景。

本项目为云岭咨询公司内部项目，致力于为公司内部提供一个灵活、可扩展、高性能的业务流程管理（BPM）和通用后台管理解决方案。

## 主要特性

*   **纯后端分离设计**：无前端代码，专注于提供强大的 API 服务，便于与任何前端框架（Vue, React, Angular等）进行集成。
*   **最新技术栈**：基于 Spring Boot 3.x 和 JDK 17，享受现代 Java 生态系统的最新成果。
*   **强大的工作流引擎**：深度整合 Flowable 7.x，提供可视化的流程设计、部署、执行、监控和历史追溯全生命周期管理。
*   **细粒度的权限控制**：基于 Spring Security 6.x，实现 Token 认证和 RBAC（基于角色的访问控制），精确控制接口和菜单权限。
*   **动态数据权限**：通过自定义注解和 AOP，轻松实现按部门、按用户等维度的数据权限过滤，适应复杂的数据隔离场景。
*   **高效代码生成**：内置高效的代码生成器，可一键生成 Controller, Service, Mapper, Domain 等后端代码，大幅提升开发效率。
*   **灵活的定时任务**：集成 Quartz，提供可视化的任务管理界面，支持 Cron 表达式，可动态添加、暂停、恢复和删除任务。
*   **全面的系统监控**：内置操作日志、登录日志、系统缓存监控等功能，便于追踪系统状态和排查问题。
*   **清晰的模块化结构**：项目按功能模块化，职责分明，高内聚低耦合，易于维护和二次开发。

## 主要模块

```
ylzx-flowable
├── ylzx-admin -- 启动模块 (核心)
├── ylzx-common -- 通用模块
├── ylzx-framework -- 框架模块 (安全、缓存等)
├── ylzx-system -- 系统模块 (用户、角色、权限等)
├── ylzx-flowable -- 工作流模块
├── ylzx-generator -- 代码生成模块
├── ylzx-quartz -- 定时任务模块
```

| 模块名          | 模块描述                                                                                                                              |
| --------------- | ------------------------------------------------------------------------------------------------------------------------------------- |
| `ylzx-admin`      | **启动入口**：项目的总启动模块，作为"胶水层"，负责整合加载所有业务和框架模块，并启动 Spring Boot 应用。                               |
| `ylzx-common`     | **通用工具库**：存放项目范围内的公共代码，如常量定义、枚举类、基础 DTO/VO、自定义异常、JSON 和日期工具类、通用返回结果封装等。             |
| `ylzx-framework`  | **核心框架层**：项目的技术基座。集成并配置 Spring Security（权限认证）、Redis（缓存）、AOP（日志、数据权限）、MyBatis（数据持久化）等。      |
| `ylzx-system`     | **核心业务功能**：实现平台基础的后台管理业务。包括：用户管理、角色管理、菜单权限、部门管理、岗位管理、参数配置、字典管理、通知公告、操作日志、登录日志等。 |
| `ylzx-flowable`   | **工作流引擎**：深度集成 Flowable，负责流程定义、流程实例、任务办理、表单挂载、监听器配置等所有与业务流程相关的功能。                   |
| `ylzx-generator`  | **代码生成器**：根据数据库表结构，快速生成该表的全套后端 CRUD 代码，包括 Controller, Service, Mapper, Domain 等，极大提升开发效率。      |
| `ylzx-quartz`     | **定时任务**：基于 Quartz 实现，提供定时任务的增、删、改、查、执行、暂停、恢复等管理功能，并记录任务执行日志。                      |

## 技术栈

| 技术             | 名称                     | 版本            | 描述                                     |
| ---------------- | ------------------------ | --------------- | ---------------------------------------- |
| 核心框架         | Spring Boot              | 3.x             | 现代 Java 应用开发框架                   |
| 工作流引擎       | Flowable                 | 7.x             | 强大的业务流程管理引擎                   |
| 安全认证         | Spring Security          | 6.x             | 提供认证、授权和攻击防护                 |
| 数据库持久化     | MyBatis                  | 3.x             | 灵活的 SQL 映射框架                      |
| 数据库连接池     | Druid                    | 1.2.x           | 高性能的数据库连接池                     |
| 数据库           | PostgreSQL               | >= 12           | 功能强大的开源对象关系数据库             |
| 缓存             | Redis                    | >= 6.0          | 高性能的内存数据结构存储                 |
| 定时任务         | Quartz                   | 2.3.x           | 成熟的作业调度框架                       |
| 日志管理         | SLF4J, Logback           | -               | 标准的日志门面和实现                     |
| 工具集           | Hutool, Jackson          | -               | 便捷的 Java 工具库和 JSON 处理库         |

## 快速开始

### 环境准备
*   JDK >= 17
*   Maven >= 3.8
*   PostgreSQL >= 12
*   Redis >= 6.0

### 安装步骤
1.  **克隆项目**
    ```bash
    git clone http://192.168.101.121:3000/qiudong/data-annotation-platform.git
    ```
2.  **数据库初始化**
    *   创建一个名为 `ylzx-flowable` 的 PostgreSQL 数据库。
    *   运行 `sql/` 目录下的 `*.sql` 文件，初始化表结构和基础数据。

3.  **修改配置**
    *   打开 `ylzx-admin/src/main/resources/application.yml`。
    *   修改 `spring.datasource` 下的数据库连接信息。
    *   修改 `spring.redis` 下的 Redis 连接信息。

4.  **启动项目**
    *   进入项目根目录。
    *   执行 `ylzx-admin` 模块下的 `YlzxApplication.java` 的 `main` 方法启动项目。

### 注意事项
*   请确保 PostgreSQL 的 `pg_trgm` 扩展已安装，部分查询功能依赖此扩展。
*   项目默认端口为 `8080`，如有冲突请在 `application.yml` 中修改。

## 版权和保密

© 2025 云岭咨询公司. All Rights Reserved.

本项目为云岭咨询公司内部资产，包含保密信息。未经公司书面许可，严禁以任何形式复制、修改、分发或向第三方披露本项目的任何部分。


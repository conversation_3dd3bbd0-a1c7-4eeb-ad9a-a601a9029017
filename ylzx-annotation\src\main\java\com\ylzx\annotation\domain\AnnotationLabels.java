package com.ylzx.annotation.domain;

import java.io.Serial;

import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注标签对象 annotation_labels
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationLabels extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注标签主键 */
    private Long labelId;

    /** 标注分类主键 */
    @Excel(name = "标注分类主键")
    private Long categoryId;

    /** 标注标签名称 */
    @Excel(name = "标注标签名称")
    private String name;

    /** 标注标签编码 */
    @Excel(name = "标注标签编码")
    private String code;

    /** 标注标签描述 */
    @Excel(name = "标注标签描述")
    private String description;

    /** 标注标签颜色 */
    @Excel(name = "标注标签颜色")
    private String colorHex;
}

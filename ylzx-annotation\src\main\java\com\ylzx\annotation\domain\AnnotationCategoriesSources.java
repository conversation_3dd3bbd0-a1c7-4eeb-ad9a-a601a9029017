package com.ylzx.annotation.domain;

import java.io.Serial;
import java.util.Date;

import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注分类数据源关联对象 annotation_categories_sources
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationCategoriesSources extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 标注分类主键 */
    @Excel(name = "标注分类主键")
    private Long categoryId;

    /** 数据源主键 */
    @Excel(name = "数据源主键")
    private Long sourceId;

    /** 关联时间 */
    @Excel(name = "关联时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date associatedAt;

    /** 关联用户ID */
    @Excel(name = "关联用户ID")
    private Long associatedByUserId;

    /** 状态 (active: 激活, inactive: 停用) */
    @Excel(name = "状态")
    private String status;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}

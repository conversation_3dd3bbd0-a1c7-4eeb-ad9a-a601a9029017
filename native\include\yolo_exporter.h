#ifndef YOLO_EXPORTER_H
#define YOLO_EXPORTER_H

#include "common.h"
#include <string>
#include <vector>

/**
 * YOLO格式导出器
 */
class YOLOExporter {
public:
    /**
     * 导出YOLO格式数据集
     * 
     * @param config 导出配置
     * @param images 图像信息列表
     * @param categories 分类信息JSON
     * @return 导出结果
     */
    ExportResult exportDataset(const ExportConfig& config, 
                              const std::vector<ImageInfo>& images, 
                              const std::string& categories);

private:
    /**
     * 创建YOLO格式的标注文件 (.txt)
     */
    bool createAnnotationTxt(const ImageInfo& image,
                            const std::string& outputPath,
                            const std::string& categories);
    
    /**
     * 处理单个图像
     */
    bool processImage(const ImageInfo& image, 
                     const ExportConfig& config,
                     const std::string& outputDir);
    
    /**
     * 创建data.yaml配置文件
     */
    bool createDataYaml(const std::vector<ImageInfo>& images,
                       const std::string& outputPath,
                       const std::string& categories);
    
    /**
     * 解析标注数据为YOLO格式
     */
    std::string parseAnnotationToYOLO(const std::string& annotationData,
                                     const ImageInfo& image,
                                     const std::string& categories);
    
    /**
     * 转换坐标格式 (绝对坐标 -> 相对坐标)
     */
    std::string convertCoordinates(const std::string& coordinates,
                                  int imageWidth, int imageHeight);
};

#endif // YOLO_EXPORTER_H

package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationImagesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 标注图片Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注图片管理")
@RestController
@RequestMapping("/annotation/images")
public class AnnotationImagesController extends BaseController
{
    @Resource
    private AnnotationImagesService annotationImagesService;

    /**
     * 查询标注图片列表
     */
    @ApiOperation("查询标注图片列表")
    @PreAuthorize("@ss.hasPermi('system:images:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") AnnotationImages annotationImages)
    {
        startPage();
        List<AnnotationImages> list = annotationImagesService.selectAnnotationImagesList(annotationImages);
        return getDataTable(list);
    }

    /**
     * 导出标注图片列表
     */
    @PreAuthorize("@ss.hasPermi('system:images:export')")
    @Log(title = "标注图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationImages annotationImages)
    {
        List<AnnotationImages> list = annotationImagesService.selectAnnotationImagesList(annotationImages);
        ExcelUtil<AnnotationImages> util = new ExcelUtil<AnnotationImages>(AnnotationImages.class);
        util.exportExcel(response, list, "标注图片数据");
    }

    /**
     * 获取标注图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:images:query')")
    @GetMapping(value = "/{imageId}")
    public AjaxResult getInfo(@PathVariable("imageId") Long imageId)
    {
        return success(annotationImagesService.selectAnnotationImagesByImageId(imageId));
    }

    /**
     * 新增标注图片
     */
    @PreAuthorize("@ss.hasPermi('system:images:add')")
    @Log(title = "标注图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationImages annotationImages)
    {
        return toAjax(annotationImagesService.insertAnnotationImages(annotationImages));
    }

    /**
     * 修改标注图片
     */
    @PreAuthorize("@ss.hasPermi('system:images:edit')")
    @Log(title = "标注图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationImages annotationImages)
    {
        return toAjax(annotationImagesService.updateAnnotationImages(annotationImages));
    }

    /**
     * 删除标注图片
     */
    @PreAuthorize("@ss.hasPermi('system:images:remove')")
    @Log(title = "标注图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable("imageIds") Long[] imageIds)
    {
        return toAjax(annotationImagesService.deleteAnnotationImagesByImageIds(imageIds));
    }

    /**
     * 设置图片
     */
    @ApiOperation("设置图片")
    @PostMapping("/set")
    public void setAnnotationImagesService()
    {
        annotationImagesService.processImagesFromSourceId(1L, 1L);
    }
}

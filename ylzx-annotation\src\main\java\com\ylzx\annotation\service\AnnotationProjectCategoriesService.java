package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationProjectCategories;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注项目分类关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface AnnotationProjectCategoriesService extends IService<AnnotationProjectCategories>
{
    /**
     * 根据项目ID查询关联的分类ID列表
     * 
     * @param projectId 项目ID
     * @return 分类ID列表
     */
    List<Long> selectCategoryIdsByProjectId(Long projectId);

    /**
     * 根据分类ID查询关联的项目ID列表
     * 
     * @param categoryId 分类ID
     * @return 项目ID列表
     */
    List<Long> selectProjectIdsByCategoryId(Long categoryId);

    /**
     * 批量插入项目分类关联关系
     * 
     * @param projectCategoriesList 项目分类关联列表
     * @return 插入成功的记录数
     */
    int insertBatch(List<AnnotationProjectCategories> projectCategoriesList);

    /**
     * 根据项目ID删除所有关联关系
     * 
     * @param projectId 项目ID
     * @return 删除的记录数
     */
    int deleteByProjectId(Long projectId);

    /**
     * 根据分类ID删除所有关联关系
     * 
     * @param categoryId 分类ID
     * @return 删除的记录数
     */
    int deleteByCategoryId(Long categoryId);

    /**
     * 检查项目和分类的关联关系是否存在
     * 
     * @param projectId 项目ID
     * @param categoryId 分类ID
     * @return 是否存在关联关系
     */
    boolean existsRelation(Long projectId, Long categoryId);

    /**
     * 更新项目的分类关联关系
     * 先删除原有关联，再插入新的关联
     * 
     * @param projectId 项目ID
     * @param categoryIds 新的分类ID列表
     * @return 是否更新成功
     */
    boolean updateProjectCategories(Long projectId, List<Long> categoryIds);

    /**
     * 批量更新多个项目的分类关联关系
     * 
     * @param projectCategoryMap 项目ID到分类ID列表的映射
     * @return 更新成功的项目数量
     */
    int batchUpdateProjectCategories(java.util.Map<Long, List<Long>> projectCategoryMap);
}

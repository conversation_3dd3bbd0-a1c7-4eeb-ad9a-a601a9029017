# 图像裁剪功能实现总结

## 概述
成功实现了基于标注坐标的智能图像裁剪功能，支持矩形和多边形标注的自动裁剪，并提供了随机位置放置算法以确保裁剪对象的均匀分布。

## 已完成的功能

### 1. 核心服务层
- **ImageCropService** - 图像裁剪服务接口
- **ImageCropServiceImpl** - 图像裁剪服务实现
- **AnnotationGeometryUtils** - 标注几何工具类

### 2. JNI集成
- **DatasetExportNative** - 增强的JNI接口，支持图像裁剪
- **native/src/image_crop.cpp** - C++图像裁剪实现
- **native/include/image_crop.h** - C++头文件定义

### 3. REST API控制器
- **ImageCropController** - 提供HTTP接口的控制器
- 支持单图像裁剪、批量裁剪、按分类批量裁剪
- 包含裁剪预览功能

### 4. 测试套件
- **ImageCropServiceTest** - 综合测试类
- 覆盖坐标解析、边界框计算、批量处理等功能

## 核心特性

### 1. 智能坐标解析
- 支持矩形标注（2个点：x1,y1,x2,y2）
- 支持多边形标注（3个以上点）
- 自动计算包围盒（Union Bounding Box）
- 处理多个标注的合并区域

### 2. 随机位置放置
- 实现均匀分布算法
- 避免对象总是居中或固定位置
- 支持可配置的填充边距
- 确保裁剪对象在目标图像范围内

### 3. 高性能批处理
- Java单线程处理（小批量）
- JNI多线程处理（大批量，≥10张图像）
- 批量处理优化，减少JNI调用开销
- 支持异步处理和进度跟踪

### 4. 灵活配置选项
```java
CropConfig config = new CropConfig();
config.setTargetWidth(512);        // 目标宽度
config.setTargetHeight(384);       // 目标高度
config.setPadding(20);              // 填充边距
config.setEnableRandomPlacement(true); // 启用随机放置
config.setMaintainAspectRatio(true);    // 保持宽高比
config.setBackgroundColor(new int[]{128, 128, 128}); // 背景色
```

## API接口

### 1. 单图像裁剪
```http
POST /api/image-crop/single
{
    "imageId": 1,
    "outputPath": "/path/to/output.jpg",
    "config": {
        "targetWidth": 512,
        "targetHeight": 384,
        "padding": 20
    }
}
```

### 2. 批量图像裁剪
```http
POST /api/image-crop/batch
{
    "imageIds": [1, 2, 3],
    "outputDir": "/path/to/output/",
    "config": {
        "targetWidth": 256,
        "targetHeight": 256
    }
}
```

### 3. 按分类批量裁剪
```http
POST /api/image-crop/batch-by-category
{
    "categoryId": 1,
    "outputDir": "/path/to/output/",
    "config": {
        "enableRandomPlacement": true,
        "padding": 30
    }
}
```

### 4. 裁剪预览
```http
GET /api/image-crop/preview/{imageId}?targetWidth=256&targetHeight=256&padding=20
```

## 技术实现细节

### 1. 坐标解析算法
- 支持多种坐标格式：逗号分隔、空格分隔、混合格式
- 自动检测矩形vs多边形标注
- 计算最小外接矩形（Bounding Box）
- 处理坐标验证和归一化

### 2. 随机放置算法
```java
private Point2f calculateRandomPosition(Size cropSize, Size objectSize, int padding) {
    int maxX = cropSize.width - objectSize.width - 2 * padding;
    int maxY = cropSize.height - objectSize.height - 2 * padding;
    
    // 使用均匀分布确保随机性
    return new Point2f(
        random.nextInt(maxX) + padding,
        random.nextInt(maxY) + padding
    );
}
```

### 3. 批处理优化
- 小批量（<10张）：使用Java处理
- 大批量（≥10张）：使用JNI多线程处理
- 自动选择最优处理策略
- 支持进度回调和错误处理

## 文件结构
```
ylzx-annotation/
├── src/main/java/com/ylzx/annotation/
│   ├── controller/ImageCropController.java
│   ├── service/ImageCropService.java
│   ├── service/impl/ImageCropServiceImpl.java
│   ├── utils/AnnotationGeometryUtils.java
│   └── jni/DatasetExportNative.java
├── src/test/java/com/ylzx/annotation/service/
│   └── ImageCropServiceTest.java
└── native/
    ├── include/image_crop.h
    ├── src/image_crop.cpp
    └── CMakeLists.txt (已更新)
```

## 下一步工作

### 1. C++库编译
- 使用CMake编译native库
- 确保OpenCV依赖正确配置
- 生成对应平台的动态库文件

### 2. 集成测试
- 创建实际图像文件进行测试
- 验证JNI调用正常工作
- 测试批处理性能

### 3. 部署配置
- 配置native库路径
- 设置图像存储目录
- 配置API访问权限

## 性能特点
- **内存效率**：流式处理，避免大量图像同时加载
- **并发处理**：JNI层支持多线程并行处理
- **错误恢复**：单个图像失败不影响批处理继续
- **进度跟踪**：实时返回处理进度和结果统计

## 兼容性
- Spring Boot 3.x
- Jakarta Validation
- MyBatis Plus
- OpenCV 4.x
- Java 17+
- 支持Windows/Linux平台

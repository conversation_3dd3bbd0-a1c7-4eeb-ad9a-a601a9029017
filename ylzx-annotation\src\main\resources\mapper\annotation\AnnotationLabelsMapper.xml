<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationLabelsMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationLabels" id="AnnotationLabelsResult">
        <result property="labelId"    column="label_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="description"    column="description"    />
        <result property="colorHex"    column="color_hex"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationLabelsVo">
        select label_id, category_id, name, code, description, color_hex, create_time, update_time, create_by, update_by from annotation_labels
    </sql>

    <select id="selectAnnotationLabelsList" parameterType="AnnotationLabels" resultMap="AnnotationLabelsResult">
        <include refid="selectAnnotationLabelsVo"/>
        <where>  
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="name != null  and name != ''"> and name like '%' || #{name} || '%'</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="colorHex != null  and colorHex != ''"> and color_hex = #{colorHex}</if>
        </where>
    </select>
    
    <select id="selectAnnotationLabelsByLabelId" parameterType="Long" resultMap="AnnotationLabelsResult">
        <include refid="selectAnnotationLabelsVo"/>
        where label_id = #{labelId}
    </select>

    <insert id="insertAnnotationLabels" parameterType="AnnotationLabels" useGeneratedKeys="true" keyProperty="labelId">
        insert into annotation_labels
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="colorHex != null">color_hex,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="description != null">#{description},</if>
            <if test="colorHex != null">#{colorHex},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationLabels" parameterType="AnnotationLabels">
        update annotation_labels
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="colorHex != null">color_hex = #{colorHex},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where label_id = #{labelId}
    </update>

    <delete id="deleteAnnotationLabelsByLabelId" parameterType="Long">
        delete from annotation_labels where label_id = #{labelId}
    </delete>

    <delete id="deleteAnnotationLabelsByLabelIds" parameterType="String">
        delete from annotation_labels where label_id in 
        <foreach item="labelId" collection="array" open="(" separator="," close=")">
            #{labelId}
        </foreach>
    </delete>
</mapper>
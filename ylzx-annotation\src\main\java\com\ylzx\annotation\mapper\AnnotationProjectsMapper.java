package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationProjects;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标注项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationProjectsMapper extends BaseMapper<AnnotationProjects>
{
    /**
     * 查询标注项目
     * 
     * @param projectId 标注项目主键
     * @return 标注项目
     */
    AnnotationProjects selectAnnotationProjectsByProjectId(Long projectId);

    /**
     * 查询标注项目列表
     * 
     * @param annotationProjects 标注项目
     * @return 标注项目集合
     */
    List<AnnotationProjects> selectAnnotationProjectsList(AnnotationProjects annotationProjects);

    /**
     * 新增标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    int insertAnnotationProjects(AnnotationProjects annotationProjects);

    /**
     * 修改标注项目
     * 
     * @param annotationProjects 标注项目
     * @return 结果
     */
    int updateAnnotationProjects(AnnotationProjects annotationProjects);

    /**
     * 删除标注项目
     * 
     * @param projectId 标注项目主键
     * @return 结果
     */
    int deleteAnnotationProjectsByProjectId(Long projectId);

    /**
     * 批量删除标注项目
     * 
     * @param projectIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationProjectsByProjectIds(Long[] projectIds);
}

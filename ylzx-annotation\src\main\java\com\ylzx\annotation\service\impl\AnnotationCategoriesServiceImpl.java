package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationCategoriesMapper;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.annotation.service.AnnotationCategoriesService;

/**
 * 标注分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationCategoriesServiceImpl extends ServiceImpl<AnnotationCategoriesMapper,AnnotationCategories> implements AnnotationCategoriesService
{
    @Autowired
    private AnnotationCategoriesMapper annotationCategoriesMapper;

    /**
     * 查询标注分类
     * 
     * @param categoryId 标注分类主键
     * @return 标注分类
     */
    @Override
    public AnnotationCategories selectAnnotationCategoriesByCategoryId(Long categoryId)
    {
        return annotationCategoriesMapper.selectAnnotationCategoriesByCategoryId(categoryId);
    }

    /**
     * 查询标注分类列表
     * 
     * @param annotationCategories 标注分类
     * @return 标注分类集合
     */
    @Override
    public List<AnnotationCategories> selectAnnotationCategoriesList(AnnotationCategories annotationCategories)
    {
        return annotationCategoriesMapper.selectAnnotationCategoriesList(annotationCategories);
    }

    /**
     * 新增标注分类
     * 
     * @param annotationCategories 标注分类
     * @return 结果
     */
    @Override
    public int insertAnnotationCategories(AnnotationCategories annotationCategories)
    {
        return annotationCategoriesMapper.insertAnnotationCategories(annotationCategories);
    }

    /**
     * 修改标注分类
     * 
     * @param annotationCategories 标注分类
     * @return 结果
     */
    @Override
    public int updateAnnotationCategories(AnnotationCategories annotationCategories)
    {
        return annotationCategoriesMapper.updateAnnotationCategories(annotationCategories);
    }

    /**
     * 批量删除标注分类
     * 
     * @param categoryIds 需要删除的标注分类主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationCategoriesByCategoryIds(Long[] categoryIds)
    {
        return annotationCategoriesMapper.deleteAnnotationCategoriesByCategoryIds(categoryIds);
    }

    /**
     * 删除标注分类信息
     * 
     * @param categoryId 标注分类主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationCategoriesByCategoryId(Long categoryId)
    {
        return annotationCategoriesMapper.deleteAnnotationCategoriesByCategoryId(categoryId);
    }
}

package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationReviews;
import com.ylzx.annotation.domain.vo.ReviewTaskWithDetailsVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注审核Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationReviewsService extends IService<AnnotationReviews>
{
    /**
     * 查询标注审核
     * 
     * @param reviewId 标注审核主键
     * @return 标注审核
     */
    AnnotationReviews selectAnnotationReviewsByReviewId(Long reviewId);

    /**
     * 查询标注审核列表
     * 
     * @param annotationReviews 标注审核
     * @return 标注审核集合
     */
    List<AnnotationReviews> selectAnnotationReviewsList(AnnotationReviews annotationReviews);

    /**
     * 新增标注审核
     * 
     * @param annotationReviews 标注审核
     * @return 结果
     */
    int insertAnnotationReviews(AnnotationReviews annotationReviews);

    /**
     * 修改标注审核
     * 
     * @param annotationReviews 标注审核
     * @return 结果
     */
    int updateAnnotationReviews(AnnotationReviews annotationReviews);

    /**
     * 批量删除标注审核
     * 
     * @param reviewIds 需要删除的标注审核主键集合
     * @return 结果
     */
    int deleteAnnotationReviewsByReviewIds(Long[] reviewIds);

    /**
     * 删除标注审核信息
     *
     * @param reviewId 标注审核主键
     * @return 结果
     */
    int deleteAnnotationReviewsByReviewId(Long reviewId);

    /**
     * 根据标注人ID查询审核记录
     *
     * @param annotatorId 标注人ID
     * @return 审核记录列表
     */
    List<AnnotationReviews> selectAnnotationReviewsByAnnotatorId(Long annotatorId);

    /**
     * 根据图片ID查询审核记录
     *
     * @param imageId 图片ID
     * @return 审核记录列表
     */
    List<AnnotationReviews> selectAnnotationReviewsByImageId(Long imageId);

    /**
     * 根据标注人ID和状态查询审核记录
     *
     * @param annotatorId 标注人ID
     * @param status 审核状态
     * @return 审核记录列表
     */
    List<AnnotationReviews> selectAnnotationReviewsByAnnotatorIdAndStatus(Long annotatorId, com.ylzx.annotation.domain.enums.AnnotationStatus status);

    /**
     * 统计标注人的审核情况
     *
     * @param annotatorId 标注人ID
     * @return 统计结果 Map，包含各种状态的数量和比例
     */
    java.util.Map<String, Object> countReviewStatsByAnnotatorId(Long annotatorId);

    /**
     * 生成抽审记录
     *
     * @param categoryId 标注分类ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param reviewRatio 抽审比例 (0-1之间的小数，如0.1表示10%)
     * @param reviewCount 抽审数量 (如果指定数量，则忽略比例)
     * @return 生成的抽审记录数量
     */
    int generateReviewRecords(Long categoryId, java.time.LocalDateTime startTime,
                             java.time.LocalDateTime endTime, java.math.BigDecimal reviewRatio,
                             Integer reviewCount);

    /**
     * 领取审核任务
     *
     * @param categoryId 标注分类ID (可选，为null时领取所有分类的任务)
     * @param limit 领取数量
     * @return 领取到的审核任务列表
     */
    List<AnnotationReviews> claimReviewTasks(Long categoryId, int limit);

    /**
     * 检查标注记录是否已存在审核记录
     *
     * @param annotationId 标注ID
     * @return 是否存在
     */
    boolean existsReviewByAnnotationId(Long annotationId);

    /**
     * 领取审核任务（返回详细信息，包含图片和标注）
     *
     * @param categoryId 标注分类ID (可选，为null时领取所有分类的任务)
     * @param limit 领取数量
     * @return 领取到的审核任务详细信息列表
     */
    List<ReviewTaskWithDetailsVo> claimReviewTasksWithDetails(Long categoryId, int limit);

    /**
     * 获取我的审核任务列表（返回详细信息，包含图片和标注）
     *
     * @param categoryId 标注分类ID (可选，为null时查询所有分类的任务)
     * @param statuses 审核状态列表 (可选)
     * @return 审核任务详细信息列表
     */
    List<ReviewTaskWithDetailsVo> getMyReviewTasksWithDetails(Long categoryId, List<com.ylzx.annotation.domain.enums.AnnotationStatus> statuses);

    /**
     * 获取单个审核任务的详细信息（包含图片和标注）
     *
     * @param reviewId 审核任务ID
     * @return 审核任务详细信息
     */
    ReviewTaskWithDetailsVo getReviewTaskWithDetails(Long reviewId);
}

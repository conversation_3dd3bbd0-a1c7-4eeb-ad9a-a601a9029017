cmake_minimum_required(VERSION 3.16)
project(dataset_export)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 静态编译配置
set(CMAKE_FIND_LIBRARY_SUFFIXES ".a")
set(BUILD_SHARED_LIBS OFF)
set(CMAKE_EXE_LINKER_FLAGS "-static")
set(CMAKE_SHARED_LINKER_FLAGS "-static-libgcc -static-libstdc++")

# 设置静态链接标志
if(WIN32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -static-libgcc -static-libstdc++")
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -static-libgcc")
endif()

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
endif()

# 查找JNI
# 手动设置JNI路径
set(JAVA_HOME "C:/java/jdk-21.0.2")
set(JNI_INCLUDE_DIRS "${JAVA_HOME}/include" "${JAVA_HOME}/include/win32")
set(JNI_LIBRARIES "")
set(JNI_FOUND TRUE)
message(STATUS "JNI_INCLUDE_DIRS=${JNI_INCLUDE_DIRS}")
message(STATUS "JNI_LIBRARIES=${JNI_LIBRARIES}")

# 查找OpenCV (可选)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    message(STATUS "Found OpenCV ${OpenCV_VERSION}")
    add_definitions(-DUSE_OPENCV)
else()
    message(STATUS "OpenCV not found, using basic image processing")
endif()

# 查找nlohmann/json
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann/json not found, will use bundled version")
    # 可以在这里添加下载或使用bundled版本的逻辑
endif()

# 包含目录
include_directories(${JNI_INCLUDE_DIRS})
if(JNI_FOUND)
    include_directories(${JAVA_INCLUDE_PATH})
    include_directories(${JAVA_INCLUDE_PATH2})
endif()
include_directories(src)
include_directories(include)
include_directories(third_party)

# 源文件
set(SOURCES
    src/dataset_export_jni.cpp
    src/coco_exporter.cpp
    src/voc_exporter.cpp
    src/yolo_exporter.cpp
    src/image_processor.cpp
    src/image_crop.cpp
    src/utils.cpp
)

# 头文件
set(HEADERS
    include/dataset_export_jni.h
    include/coco_exporter.h
    include/voc_exporter.h
    include/yolo_exporter.h
    include/image_processor.h
    include/image_crop.h
    include/common.h
)

# 创建动态库
add_library(dataset_export SHARED ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(dataset_export ${JNI_LIBRARIES})

if(OpenCV_FOUND)
    target_link_libraries(dataset_export ${OpenCV_LIBS})
endif()

if(nlohmann_json_FOUND)
    target_link_libraries(dataset_export nlohmann_json::nlohmann_json)
endif()

# 静态链接系统库
if(WIN32)
    target_link_libraries(dataset_export -static-libgcc -static-libstdc++ -Wl,-Bstatic -lstdc++ -lpthread -Wl,-Bdynamic)
endif()

# 设置输出目录
set_target_properties(dataset_export PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows特定设置
if(WIN32)
    set_target_properties(dataset_export PROPERTIES
        PREFIX ""
        SUFFIX ".dll"
    )
endif()

# 安装规则
install(TARGETS dataset_export
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# 复制到Java项目的resources目录
if(EXISTS "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native")
    add_custom_command(TARGET dataset_export POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:dataset_export> 
        "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native/"
        COMMENT "Copying library to Java resources directory"
    )
endif()

# 生成JNI头文件的自定义目标
add_custom_target(generate_jni_headers
    COMMAND javac -h ${CMAKE_SOURCE_DIR}/include 
    -cp "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/java"
    "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/java/com/ylzx/annotation/jni/DatasetExportNative.java"
    COMMENT "Generating JNI headers"
    VERBATIM
)

# 打印配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C++ flags: ${CMAKE_CXX_FLAGS}")

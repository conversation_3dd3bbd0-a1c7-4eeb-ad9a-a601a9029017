package com.ylzx.annotation.domain;

import java.io.Serial;
import java.util.Date;


import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注项目图片对象 annotation_project_images
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationCategoriesImages extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 标注项目主键 */
    private Long categoryId;

    /** 标注图片主键 */
    private Long imageId;

    /** 标注员ID */
    private Long annotatorId;

    /** 领取时间 */
    private Date claimTime;

    /** 标注状态 0:未标注 1:标注中 2:待审核 3:审核通过 4:审核不通过 */
    @Builder.Default
    private AnnotationStatus status = AnnotationStatus.NOT_ANNOTATED;
}
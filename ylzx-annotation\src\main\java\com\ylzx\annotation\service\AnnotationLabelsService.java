package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationLabels;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注标签Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationLabelsService extends IService<AnnotationLabels>
{
    /**
     * 查询标注标签
     * 
     * @param labelId 标注标签主键
     * @return 标注标签
     */
    AnnotationLabels selectAnnotationLabelsByLabelId(Long labelId);

    /**
     * 查询标注标签列表
     * 
     * @param annotationLabels 标注标签
     * @return 标注标签集合
     */
    List<AnnotationLabels> selectAnnotationLabelsList(AnnotationLabels annotationLabels);

    /**
     * 新增标注标签
     * 
     * @param annotationLabels 标注标签
     * @return 结果
     */
    int insertAnnotationLabels(AnnotationLabels annotationLabels);

    /**
     * 修改标注标签
     * 
     * @param annotationLabels 标注标签
     * @return 结果
     */
    int updateAnnotationLabels(AnnotationLabels annotationLabels);

    /**
     * 批量删除标注标签
     * 
     * @param labelIds 需要删除的标注标签主键集合
     * @return 结果
     */
    int deleteAnnotationLabelsByLabelIds(Long[] labelIds);

    /**
     * 删除标注标签信息
     * 
     * @param labelId 标注标签主键
     * @return 结果
     */
    int deleteAnnotationLabelsByLabelId(Long labelId);
}

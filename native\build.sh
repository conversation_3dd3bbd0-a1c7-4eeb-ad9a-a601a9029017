#!/bin/bash
# Linux/macOS构建脚本

echo "开始构建数据集导出本地库..."

# 创建构建目录
mkdir -p build
cd build

# 配置CMake
echo "配置CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo "CMake配置失败"
    exit 1
fi

# 构建项目
echo "构建项目..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "构建失败"
    exit 1
fi

echo "构建完成！"

# 检查库文件
if [ -f "lib/libdataset_export.so" ]; then
    echo "库文件位置: build/lib/libdataset_export.so"
elif [ -f "lib/libdataset_export.dylib" ]; then
    echo "库文件位置: build/lib/libdataset_export.dylib"
fi

# 复制到Java资源目录
if [ -d "../ylzx-annotation/src/main/resources/native" ]; then
    echo "复制库文件到Java资源目录..."
    if [ -f "lib/libdataset_export.so" ]; then
        cp lib/libdataset_export.so ../ylzx-annotation/src/main/resources/native/
    elif [ -f "lib/libdataset_export.dylib" ]; then
        cp lib/libdataset_export.dylib ../ylzx-annotation/src/main/resources/native/
    fi
fi

echo "构建脚本执行完成"

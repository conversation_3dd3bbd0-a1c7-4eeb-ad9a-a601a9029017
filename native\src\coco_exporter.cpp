#include "coco_exporter.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <ctime>

ExportResult COCOExporter::exportDataset(const ExportConfig& config, 
                                         const std::vector<ImageInfo>& images, 
                                         const std::string& categories) {
    ExportResult result;
    result.success = false;
    result.processedCount = 0;
    result.outputPath = config.outputPath;
    
    try {
        // 创建输出目录结构
        std::filesystem::create_directories(config.outputPath + "/train2017");
        std::filesystem::create_directories(config.outputPath + "/val2017");
        std::filesystem::create_directories(config.outputPath + "/test2017");
        std::filesystem::create_directories(config.outputPath + "/annotations");
        
        // 按数据集类型分组处理图像
        std::vector<ImageInfo> trainImages, valImages, testImages;
        for (const auto& image : images) {
            switch (image.datasetType) {
                case DatasetType::TRAIN:
                    trainImages.push_back(image);
                    break;
                case DatasetType::VALIDATION:
                    valImages.push_back(image);
                    break;
                case DatasetType::TEST:
                    testImages.push_back(image);
                    break;
            }
        }
        
        // 处理各个数据集
        if (!trainImages.empty()) {
            createAnnotationsFile(config.outputPath + "/annotations/instances_train2017.json",
                                trainImages, categories, DatasetType::TRAIN);
            for (const auto& image : trainImages) {
                processImage(image, config, config.outputPath + "/train2017");
                result.processedCount++;
            }
        }
        
        if (!valImages.empty()) {
            createAnnotationsFile(config.outputPath + "/annotations/instances_val2017.json",
                                valImages, categories, DatasetType::VALIDATION);
            for (const auto& image : valImages) {
                processImage(image, config, config.outputPath + "/val2017");
                result.processedCount++;
            }
        }
        
        if (!testImages.empty()) {
            createAnnotationsFile(config.outputPath + "/annotations/instances_test2017.json",
                                testImages, categories, DatasetType::TEST);
            for (const auto& image : testImages) {
                processImage(image, config, config.outputPath + "/test2017");
                result.processedCount++;
            }
        }
        
        result.success = true;
        result.message = "COCO格式导出成功";
        
    } catch (const std::exception& e) {
        result.success = false;
        result.message = std::string("COCO导出失败: ") + e.what();
    }
    
    return result;
}

bool COCOExporter::createAnnotationsFile(const std::string& outputPath,
                                        const std::vector<ImageInfo>& images,
                                        const std::string& categories,
                                        DatasetType datasetType) {
    try {
        std::ofstream file(outputPath);
        if (!file.is_open()) {
            return false;
        }
        
        // 获取当前时间
        auto now = std::time(nullptr);
        auto tm = *std::localtime(&now);

        // 创建基本的COCO JSON结构
        file << "{\n";
        file << "  \"info\": {\n";
        file << "    \"description\": \"Dataset exported from annotation platform\",\n";
        file << "    \"url\": \"\",\n";
        file << "    \"version\": \"1.0\",\n";
        file << "    \"year\": " << (tm.tm_year + 1900) << ",\n";
        file << "    \"contributor\": \"Annotation Platform\",\n";
        file << "    \"date_created\": \"" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "\"\n";
        file << "  },\n";
        file << "  \"licenses\": [\n";
        file << "    {\n";
        file << "      \"id\": 1,\n";
        file << "      \"name\": \"Unknown License\",\n";
        file << "      \"url\": \"\"\n";
        file << "    }\n";
        file << "  ],\n";
        file << "  \"images\": [\n";
        
        // 添加图像信息
        for (size_t i = 0; i < images.size(); i++) {
            const auto& image = images[i];
            file << "    {\n";
            file << "      \"id\": " << image.imageId << ",\n";
            file << "      \"width\": " << image.originalWidth << ",\n";
            file << "      \"height\": " << image.originalHeight << ",\n";
            file << "      \"file_name\": \"" << std::filesystem::path(image.imagePath).filename().string() << "\"\n";
            file << "    }";
            if (i < images.size() - 1) file << ",";
            file << "\n";
        }
        
        file << "  ],\n";
        file << "  \"annotations\": [\n";
        
        // 添加标注信息 (这里需要解析annotationData)
        // 暂时使用占位符
        file << "  ],\n";
        file << "  \"categories\": " << categories << "\n";
        file << "}\n";
        
        file.close();
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "创建COCO标注文件失败: " << e.what() << std::endl;
        return false;
    }
}

bool COCOExporter::processImage(const ImageInfo& image, 
                               const ExportConfig& config,
                               const std::string& outputDir) {
    try {
        // 这里应该调用ImageProcessor来处理图像变换
        // 暂时使用简单的文件复制
        std::string outputPath = outputDir + "/" + std::filesystem::path(image.imagePath).filename().string();
        std::filesystem::copy_file(image.imagePath, outputPath, std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "处理图像失败: " << e.what() << std::endl;
        return false;
    }
}

std::string COCOExporter::parseAnnotationData(const std::string& annotationData) {
    // 这里需要解析JSON格式的标注数据并转换为COCO格式
    // 暂时返回空字符串
    return "[]";
}

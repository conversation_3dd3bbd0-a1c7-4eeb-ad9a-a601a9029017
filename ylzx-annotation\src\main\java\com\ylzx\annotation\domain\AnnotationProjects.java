package com.ylzx.annotation.domain;

import java.io.Serial;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注项目对象 annotation_projects
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationProjects extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注项目主键 */
    private Long projectId;

    /** 标注项目名称 */
    @Excel(name = "标注项目名称")
    private String name;

    /** 标注项目描述 */
    @Excel(name = "标注项目描述")
    private String description;

    /** 标注状态 */
    @Excel(name = "标注状态", readConverterExp = "0=未标注,1=未审核,2=审核通过,3=审核不通过")
//    @EnumValue
//    private AnnotationStatus status;
    private String status;

    // ========== 项目配置字段 ==========

    /** 宽度 */
    @Excel(name = "宽度")
    private Long width;

    /** 高度 */
    @Excel(name = "高度")
    private Long height;

    /** 转换 */
    @Excel(name = "转换")
    private String transformations;

    /** 导出格式 (coco, voc, yolo) */
    @Excel(name = "导出格式")
    private String exportFormat;

    /** 训练集比例 */
    @Excel(name = "训练集比例")
    private BigDecimal trainRatio;

    /** 验证集比例 */
    @Excel(name = "验证集比例")
    private BigDecimal validationRatio;

    /** 测试集比例 */
    @Excel(name = "测试集比例")
    private BigDecimal testRatio;

    /** 导出路径 */
    @Excel(name = "导出路径")
    private String exportPath;

    /** 增量导出 */
    @Excel(name = "增量导出")
    private Boolean incrementalExport;
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationProjectCategoriesMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationProjectCategories" id="AnnotationProjectCategoriesResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationProjectCategoriesVo">
        select id, project_id, category_id, status, create_time, update_time, create_by, update_by 
        from annotation_project_categories
    </sql>

    <select id="selectCategoryIdsByProjectId" parameterType="Long" resultType="Long">
        select category_id from annotation_project_categories 
        where project_id = #{projectId} and status = 'active'
    </select>

    <select id="selectProjectIdsByCategory" parameterType="Long" resultType="Long">
        select project_id from annotation_project_categories 
        where category_id = #{categoryId} and status = 'active'
    </select>

    <select id="existsRelation" resultType="boolean">
        select count(1) > 0 from annotation_project_categories 
        where project_id = #{projectId} and category_id = #{categoryId}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into annotation_project_categories (
            project_id, category_id, status, create_time, update_time, create_by, update_by
        ) 
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.projectId}, #{item.categoryId}, #{item.status}, 
                #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <delete id="deleteByProjectId" parameterType="Long">
        delete from annotation_project_categories where project_id = #{projectId}
    </delete>

    <delete id="deleteByCategoryId" parameterType="Long">
        delete from annotation_project_categories where category_id = #{categoryId}
    </delete>

</mapper>

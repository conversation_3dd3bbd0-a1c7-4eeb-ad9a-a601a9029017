# 数据集导出本地库

这是一个用于数据集导出的C++本地库，通过JNI与Java应用程序集成。

## 功能特性

- 支持多种导出格式：COCO、VOC、YOLO
- 图像变换处理：缩放、旋转、灰度转换、mask应用
- 高性能的C++实现
- 跨平台支持（Windows、Linux、macOS）

## 构建要求

### 必需依赖
- CMake 3.16+
- C++17兼容的编译器
- JDK 8+ (用于JNI头文件)

### 可选依赖
- OpenCV 4.x (用于高级图像处理功能)
- nlohmann/json (用于JSON处理)

## 构建说明

### Windows
```bash
# 运行构建脚本
build.bat
```

### Linux/macOS
```bash
# 给脚本执行权限
chmod +x build.sh
# 运行构建脚本
./build.sh
```

### 手动构建
```bash
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

## 目录结构

```
native/
├── CMakeLists.txt          # CMake配置文件
├── build.bat              # Windows构建脚本
├── build.sh               # Linux/macOS构建脚本
├── include/               # 头文件目录
│   ├── common.h           # 通用定义
│   ├── dataset_export_jni.h  # JNI接口
│   ├── coco_exporter.h    # COCO导出器
│   ├── voc_exporter.h     # VOC导出器
│   ├── yolo_exporter.h    # YOLO导出器
│   └── image_processor.h  # 图像处理器
└── src/                   # 源文件目录
    ├── dataset_export_jni.cpp  # JNI实现
    ├── coco_exporter.cpp   # COCO导出器实现
    ├── voc_exporter.cpp    # VOC导出器实现
    ├── yolo_exporter.cpp   # YOLO导出器实现
    ├── image_processor.cpp # 图像处理器实现
    └── utils.cpp          # 工具函数
```

## 使用说明

1. 构建本地库
2. 确保生成的动态库文件在Java应用程序的库路径中
3. 在Java代码中使用`DatasetExportNative`类调用本地方法

## 开发说明

### 添加新的导出格式
1. 在`include/`目录下创建新的头文件
2. 在`src/`目录下实现对应的源文件
3. 在`CMakeLists.txt`中添加新文件
4. 在JNI接口中添加相应的方法

### 图像处理扩展
- 如果安装了OpenCV，库会自动使用OpenCV进行高级图像处理
- 否则会使用基础的文件操作作为降级方案

## 注意事项

- 确保JNI头文件路径正确
- 在生产环境中建议使用Release模式构建
- 大文件处理时注意内存使用
- 跨平台部署时注意库文件的命名和路径

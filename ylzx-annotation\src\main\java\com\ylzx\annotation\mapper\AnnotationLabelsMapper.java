package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationLabels;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标注标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationLabelsMapper extends BaseMapper<AnnotationLabels>
{
    /**
     * 查询标注标签
     * 
     * @param labelId 标注标签主键
     * @return 标注标签
     */
    AnnotationLabels selectAnnotationLabelsByLabelId(Long labelId);

    /**
     * 查询标注标签列表
     * 
     * @param annotationLabels 标注标签
     * @return 标注标签集合
     */
    List<AnnotationLabels> selectAnnotationLabelsList(AnnotationLabels annotationLabels);

    /**
     * 新增标注标签
     * 
     * @param annotationLabels 标注标签
     * @return 结果
     */
    int insertAnnotationLabels(AnnotationLabels annotationLabels);

    /**
     * 修改标注标签
     * 
     * @param annotationLabels 标注标签
     * @return 结果
     */
    int updateAnnotationLabels(AnnotationLabels annotationLabels);

    /**
     * 删除【请填写功能名称】
     * 
     * @param labelId 标注标签主键
     * @return 结果
     */
    int deleteAnnotationLabelsByLabelId(Long labelId);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param labelIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationLabelsByLabelIds(Long[] labelIds);
}
